# Access Control Guide for TeamCheck

This guide explains different approaches to restrict access to your application while it's hosted on Vercel, allowing only the landing page to be accessible to regular users while giving you full admin access.

## Current Implementation: Environment-Based Access Control

### How It Works
The application now includes an access control system that:
1. Checks for a maintenance mode environment variable
2. Allows admin override via URL parameter or stored access
3. Redirects unauthorized users to the landing page

### Setup Instructions

#### 1. Set Environment Variables in Vercel
Go to your Vercel dashboard → Project Settings → Environment Variables and add:

```
REACT_APP_MAINTENANCE_MODE=true
REACT_APP_ADMIN_KEY=your-super-secret-key-here
```

**Important:** Replace `your-super-secret-key-here` with a strong, unique key.

#### 2. Admin Access Methods

**Method A: URL Parameter (Recommended)**
Visit your site with the admin key as a parameter:
```
https://yoursite.vercel.app/?admin=your-super-secret-key-here
```

**Method B: Direct Admin Login**
If you implement the MaintenancePage component, users will see a maintenance message with an "Admin Access" button.

#### 3. How to Enable/Disable Maintenance Mode

**Enable Maintenance Mode:**
- Set `REACT_APP_MAINTENANCE_MODE=true` in Vercel
- Redeploy or wait for automatic deployment

**Disable Maintenance Mode:**
- Set `REACT_APP_MAINTENANCE_MODE=false` in Vercel
- Or remove the environment variable entirely

### What's Protected
- ✅ Landing page (`/`) - Always accessible
- 🔒 Login/Signup pages - Restricted in maintenance mode
- 🔒 Dashboard and all sub-routes - Restricted in maintenance mode
- 🔒 Onboarding pages - Restricted in maintenance mode

## Alternative Approaches

### Approach 2: IP-Based Restriction (Vercel Pro/Enterprise)
If you have Vercel Pro or Enterprise, you can use Vercel's built-in access control:

1. Go to Project Settings → Security
2. Add your IP address to the allowlist
3. Enable "Restrict Access"

### Approach 3: Password Protection (Vercel Pro/Enterprise)
1. Go to Project Settings → Security
2. Enable "Password Protection"
3. Set a password that only you know

### Approach 4: Custom Middleware (Advanced)
Create a middleware file to handle access control at the edge:

```javascript
// middleware.js
import { NextResponse } from 'next/server';

export function middleware(request) {
  const isMaintenanceMode = process.env.MAINTENANCE_MODE === 'true';
  const adminKey = request.nextUrl.searchParams.get('admin');
  const validAdminKey = process.env.ADMIN_KEY;
  
  if (isMaintenanceMode && adminKey !== validAdminKey) {
    if (request.nextUrl.pathname !== '/') {
      return NextResponse.redirect(new URL('/', request.url));
    }
  }
  
  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
```

### Approach 5: Temporary Domain/Subdomain
1. Deploy to a temporary subdomain (e.g., `preview-teamcheck.vercel.app`)
2. Keep the main domain pointing to a simple "Coming Soon" page
3. Switch domains when ready to go live

## Security Considerations

### Current Implementation Security
- ✅ Admin key is stored in environment variables (secure)
- ✅ Access is checked on every route
- ⚠️ Admin access is stored in localStorage (cleared when user clears browser data)
- ⚠️ Admin key is visible in URL when using URL parameter method

### Recommendations
1. **Use a strong admin key** (at least 32 characters, random)
2. **Clear admin access** when done: `localStorage.removeItem('adminAccess')`
3. **Monitor access logs** in Vercel dashboard
4. **Change admin key regularly** if using long-term

## Quick Commands

### Enable Maintenance Mode
```bash
# Using Vercel CLI
vercel env add REACT_APP_MAINTENANCE_MODE production
# Enter: true
```

### Disable Maintenance Mode
```bash
# Using Vercel CLI
vercel env rm REACT_APP_MAINTENANCE_MODE production
```

### Check Current Environment Variables
```bash
vercel env ls
```

## Troubleshooting

### Issue: Can't access admin panel
- Check if `REACT_APP_ADMIN_KEY` is set correctly
- Verify the admin key you're using matches the environment variable
- Clear browser cache and localStorage

### Issue: Maintenance mode not working
- Ensure `REACT_APP_MAINTENANCE_MODE=true` is set
- Check that the environment variable is set for the correct environment (production)
- Redeploy the application

### Issue: Users can still access restricted pages
- Verify environment variables are set correctly
- Check browser network tab to ensure the latest deployment is being served
- Clear CDN cache if using one

## Best Practices

1. **Test thoroughly** before enabling maintenance mode in production
2. **Have a backup plan** to quickly disable maintenance mode
3. **Communicate with users** about maintenance windows
4. **Monitor application** during maintenance mode
5. **Document your admin key** securely (password manager)

## Support

If you need help with any of these approaches, check:
1. Vercel documentation: https://vercel.com/docs
2. React Router documentation for routing issues
3. Environment variables troubleshooting in Vercel dashboard
