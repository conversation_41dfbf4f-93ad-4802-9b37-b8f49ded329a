# TeamCheck API Documentation

## Overview

This document provides comprehensive documentation for the TeamCheck API. It outlines all available endpoints, request/response formats, authentication requirements, and error handling.

## Base URL

```
http://localhost:3000/api
```

For production:
```
https://api.teamcheck.com/api
```

## Authentication

The API uses JWT (JSON Web Token) for authentication.

### Authentication Flow

1. Register or login to obtain a JWT token
2. Include the token in the `Authorization` header of subsequent requests
3. Token format: `Bearer <your_token>`

### Token Expiration

- Access tokens expire after 24 hours
- Use the refresh token endpoint to obtain a new access token

## Response Format

All API responses follow a consistent format:

### Success Response

```json
{
  "success": true,
  "data": { ... }  // Response data varies by endpoint
}
```

### Error Response

```json
{
  "success": false,
  "message": "Error message describing what went wrong"
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | OK - Request succeeded |
| 201 | Created - Resource created successfully |
| 400 | Bad Request - Invalid input |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Resource already exists |
| 500 | Internal Server Error - Server error |

## API Endpoints

### Authentication

#### Register

- **URL**: `/auth/register`
- **Method**: `POST`
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "John Doe",
    "position": "Developer",
    "workspaceId": "optional-workspace-id"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "position": "Developer",
      "role": "employee",
      "workspaceId": "workspace-id",
      "dateJoined": "2023-01-01T00:00:00.000Z",
      "status": "active"
    },
    "token": "jwt-token"
  }
  ```

#### Login

- **URL**: `/auth/login`
- **Method**: `POST`
- **Auth Required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "position": "Developer",
      "role": "employee",
      "workspaceId": "workspace-id"
    },
    "token": "jwt-token",
    "refreshToken": "refresh-token"
  }
  ```

#### Get Current User

- **URL**: `/auth/me`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**: 
  ```json
  {
    "success": true,
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "position": "Developer",
      "role": "employee",
      "workspaceId": "workspace-id"
    }
  }
  ```

### Workspaces

#### Get All Workspaces

- **URL**: `/workspaces`
- **Method**: `GET`
- **Auth Required**: Yes
- **Success Response**: 
  ```json
  {
    "success": true,
    "workspaces": [
      {
        "id": "workspace-id",
        "name": "My Workspace",
        "description": "Workspace description",
        "logo": "logo-url",
        "ownerId": "owner-id",
        "checkInHoursStart": "09:00",
        "checkInHoursEnd": "17:00",
        "messageFormat": "format-string",
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ]
  }
  ```

#### Create Workspace

- **URL**: `/workspaces`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "name": "New Workspace",
    "description": "Workspace description",
    "logo": "logo-url",
    "checkInHoursStart": "09:00",
    "checkInHoursEnd": "17:00",
    "messageFormat": "format-string"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "workspace": {
      "id": "workspace-id",
      "name": "New Workspace",
      "description": "Workspace description",
      "logo": "logo-url",
      "ownerId": "owner-id",
      "checkInHoursStart": "09:00",
      "checkInHoursEnd": "17:00",
      "messageFormat": "format-string",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

#### Get Workspace by ID

- **URL**: `/workspaces/:id`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**: `id=[workspace-id]`
- **Success Response**: 
  ```json
  {
    "success": true,
    "workspace": {
      "id": "workspace-id",
      "name": "My Workspace",
      "description": "Workspace description",
      "logo": "logo-url",
      "ownerId": "owner-id",
      "checkInHoursStart": "09:00",
      "checkInHoursEnd": "17:00",
      "messageFormat": "format-string",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

#### Get Workspace Users

- **URL**: `/workspaces/:id/users`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**: `id=[workspace-id]`
- **Success Response**: 
  ```json
  {
    "success": true,
    "users": [
      {
        "id": "user-id",
        "email": "<EMAIL>",
        "name": "John Doe",
        "position": "Developer",
        "role": "employee",
        "avatar": "avatar-url",
        "dateJoined": "2023-01-01T00:00:00.000Z",
        "department": "Engineering",
        "status": "active",
        "workspaceId": "workspace-id"
      }
    ]
  }
  ```

### Users

#### Get Users

- **URL**: `/users?workspaceId=workspace-id`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**: 
  - `workspaceId=[workspace-id]` (required)
  - `status=[active|inactive|on leave]` (optional)
  - `role=[admin|manager|employee]` (optional)
  - `search=[search-term]` (optional)
- **Success Response**: 
  ```json
  {
    "success": true,
    "users": [
      {
        "id": "user-id",
        "email": "<EMAIL>",
        "name": "John Doe",
        "position": "Developer",
        "role": "employee",
        "avatar": "avatar-url",
        "dateJoined": "2023-01-01T00:00:00.000Z",
        "department": "Engineering",
        "status": "active",
        "workspaceId": "workspace-id"
      }
    ]
  }
  ```

#### Get User by ID

- **URL**: `/users/:id`
- **Method**: `GET`
- **Auth Required**: Yes
- **URL Parameters**: `id=[user-id]`
- **Success Response**: 
  ```json
  {
    "success": true,
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "John Doe",
      "position": "Developer",
      "role": "employee",
      "avatar": "avatar-url",
      "dateJoined": "2023-01-01T00:00:00.000Z",
      "department": "Engineering",
      "status": "active",
      "workspaceId": "workspace-id"
    }
  }
  ```

### Projects

#### Get Projects

- **URL**: `/projects`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**: 
  - `workspaceId=[workspace-id]` (optional)
  - `status=[not started|in progress|completed]` (optional)
  - `search=[search-term]` (optional)
- **Success Response**: 
  ```json
  {
    "success": true,
    "projects": [
      {
        "id": "project-id",
        "workspaceId": "workspace-id",
        "title": "Project Title",
        "description": "Project description",
        "createdBy": "user-id",
        "createdAt": "2023-01-01T00:00:00.000Z",
        "dueDate": "2023-02-01T00:00:00.000Z",
        "status": "in progress",
        "projectFormat": "statuses",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ]
  }
  ```

#### Create Project

- **URL**: `/projects`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "workspaceId": "workspace-id",
    "title": "New Project",
    "description": "Project description",
    "dueDate": "2023-02-01T00:00:00.000Z",
    "projectFormat": "statuses"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "project": {
      "id": "project-id",
      "workspaceId": "workspace-id",
      "title": "New Project",
      "description": "Project description",
      "createdBy": "user-id",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "dueDate": "2023-02-01T00:00:00.000Z",
      "status": "not started",
      "projectFormat": "statuses",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

### Tasks

#### Get Tasks

- **URL**: `/tasks`
- **Method**: `GET`
- **Auth Required**: Yes
- **Query Parameters**: 
  - `projectId=[project-id]` (optional)
  - `status=[not started|in progress|completed]` (optional)
  - `columnId=[column-id]` (optional)
  - `search=[search-term]` (optional)
- **Success Response**: 
  ```json
  {
    "success": true,
    "tasks": [
      {
        "id": "task-id",
        "workspaceId": "workspace-id",
        "projectId": "project-id",
        "title": "Task Title",
        "description": "Task description",
        "status": "in progress",
        "columnId": "column-id",
        "dueDate": "2023-02-01T00:00:00.000Z",
        "createdBy": "user-id",
        "createdAt": "2023-01-01T00:00:00.000Z",
        "updatedAt": "2023-01-01T00:00:00.000Z"
      }
    ]
  }
  ```

#### Create Task

- **URL**: `/tasks`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "workspaceId": "workspace-id",
    "projectId": "project-id",
    "title": "New Task",
    "description": "Task description",
    "status": "not started",
    "columnId": "column-id",
    "dueDate": "2023-02-01T00:00:00.000Z"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "task": {
      "id": "task-id",
      "workspaceId": "workspace-id",
      "projectId": "project-id",
      "title": "New Task",
      "description": "Task description",
      "status": "not started",
      "columnId": "column-id",
      "dueDate": "2023-02-01T00:00:00.000Z",
      "createdBy": "user-id",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

### Attendance

#### Check In

- **URL**: `/attendance/check-in`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "message": "Working on Project X today"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "record": {
      "id": "attendance-id",
      "workspaceId": "workspace-id",
      "userId": "user-id",
      "date": "2023-01-01",
      "checkInTime": "2023-01-01T09:00:00.000Z",
      "checkInMessage": "Working on Project X today",
      "status": "present",
      "createdAt": "2023-01-01T09:00:00.000Z",
      "updatedAt": "2023-01-01T09:00:00.000Z"
    }
  }
  ```

#### Check Out

- **URL**: `/attendance/check-out`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "message": "Completed tasks for Project X"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "record": {
      "id": "attendance-id",
      "workspaceId": "workspace-id",
      "userId": "user-id",
      "date": "2023-01-01",
      "checkInTime": "2023-01-01T09:00:00.000Z",
      "checkInMessage": "Working on Project X today",
      "checkOutTime": "2023-01-01T17:00:00.000Z",
      "checkOutMessage": "Completed tasks for Project X",
      "status": "present",
      "hoursWorked": 8.0,
      "createdAt": "2023-01-01T09:00:00.000Z",
      "updatedAt": "2023-01-01T17:00:00.000Z"
    }
  }
  ```

### Leaves

#### Request Leave

- **URL**: `/leaves`
- **Method**: `POST`
- **Auth Required**: Yes
- **Request Body**:
  ```json
  {
    "startDate": "2023-02-01",
    "endDate": "2023-02-05",
    "reason": "Vacation"
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "leave": {
      "id": "leave-id",
      "workspaceId": "workspace-id",
      "userId": "user-id",
      "startDate": "2023-02-01",
      "endDate": "2023-02-05",
      "reason": "Vacation",
      "status": "pending",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

### Evaluations

#### Create Evaluation

- **URL**: `/evaluations`
- **Method**: `POST`
- **Auth Required**: Yes (Manager or Admin)
- **Request Body**:
  ```json
  {
    "workspaceId": "workspace-id",
    "type": "periodic",
    "employeeId": "employee-id",
    "date": "2023-02-01",
    "projectId": "project-id" // Optional, for project evaluations
  }
  ```
- **Success Response**: 
  ```json
  {
    "success": true,
    "evaluation": {
      "id": "evaluation-id",
      "workspaceId": "workspace-id",
      "type": "periodic",
      "employeeId": "employee-id",
      "evaluatorId": "evaluator-id",
      "projectId": "project-id",
      "date": "2023-02-01",
      "status": "in progress",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
  ```

## Pagination

For endpoints that return multiple items, pagination is supported with the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

Example response with pagination:

```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- 100 requests per 15-minute window per IP address
- When the limit is exceeded, a 429 Too Many Requests response is returned

## CORS

The API supports Cross-Origin Resource Sharing (CORS) for frontend applications from the following origins:

- `http://localhost:3000` (development)
- `https://teamcheck.com` (production)

## Versioning

The current API version is v1. The version is included in the base URL:

```
/api/v1/...
```

## Support

For API support, please contact:
- Email: <EMAIL>
- Documentation: https://docs.teamcheck.com
