{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@floating-ui/react-dom": "^2.1.2", "@headlessui/react": "^2.2.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.18.0", "@react-three/postprocessing": "^2.19.1", "@tailwindcss/vite": "^4.1.7", "@tanstack/react-query": "^5.76.1", "framer-motion": "^10.18.0", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.9.0", "lucide-react": "^0.510.0", "postprocessing": "^6.37.3", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0", "react-spinners": "^0.17.0", "recharts": "^2.15.3", "tailwindcss": "^4.1.7", "three": "^0.159.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}