import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Provider } from 'react-redux';
import { store } from './store';
import SignupPage from "./pages/SignupPage";
import LoginPage from "./pages/LoginPage";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";
import DashboardLayout from "./components/layout/DashboardLayout";
import DashboardPage from "./pages/dashboard/DashboardPage";
import ProjectsPage from "./pages/dashboard/ProjectsPage";
import ProjectDetailPage from "./pages/dashboard/ProjectDetailPage";
import PerformanceEvaluationsPage from "./pages/dashboard/PerformanceEvaluationsPage";
import EvaluationDetailsPage from "./pages/dashboard/EvaluationDetailsPage";
import AttendancePage from "./pages/dashboard/AttendancePage";
import CheckInLogsPage from "./pages/dashboard/attendance/CheckInLogsPage";
import HourMapPage from "./pages/activities/HourMapPage";
import EmployeesPage from "./pages/dashboard/EmployeesPage";
import EmployeeDetailsPage from "./pages/dashboard/EmployeeDetailsPage";
import LeavesPage from "./pages/dashboard/LeavesPage";
import SettingsPage from "./pages/dashboard/SettingsPage";
import IntegrationsPage from "./pages/dashboard/settings/IntegrationsPage";
import BillingPage from "./pages/dashboard/settings/BillingPage";

// Onboarding pages
import EmailVerificationPage from "./pages/onboarding/EmailVerificationPage";
import CompanySetupPage from "./pages/onboarding/CompanySetupPage";
import InviteMembersPage from "./pages/onboarding/InviteMembersPage";

// protected route
import ProtectedRoute from "./components/auth/ProtectedRoute";

function App() {
  return (
    <Provider store={store}>
      <Router>
      <Routes>
        {/* Auth routes */}
        <Route path="/" element={<Navigate to="/login" />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/signup" element={<SignupPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/reset-password" element={<ResetPasswordPage />} />

        {/* Onboarding routes */}
        <Route path="/onboarding">
          <Route path="verify-email" element={<EmailVerificationPage />} />
          <Route path="company" element={<CompanySetupPage />} />
          <Route path="invite" element={<InviteMembersPage />} />
        </Route>

        {/* Dashboard routes */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
          }
        >
          <Route index element={<DashboardPage />} />
          <Route path="projects" element={<ProjectsPage />} />
          <Route path="projects/:projectId" element={<ProjectDetailPage />} />
          {/* Activities main route */}
          <Route
            path="activities"
            element={<Navigate to="/dashboard/activities/overview" />}
          />
          <Route path="evaluations" element={<PerformanceEvaluationsPage />} />
          <Route path="evaluations/:id" element={<EvaluationDetailsPage />} />
          <Route path="attendance/overview" element={<AttendancePage />} />
          <Route path="attendance/logs" element={<CheckInLogsPage />} />

          {/* Activities routes */}
          <Route path="activities/overview" element={<AttendancePage />} />
          <Route path="activities/logs" element={<CheckInLogsPage />} />
          <Route path="activities/hourmap" element={<HourMapPage />} />

          {/* Legacy attendance routes - redirect to activities */}
          <Route
            path="attendance/overview"
            element={<Navigate to="/dashboard/activities/overview" />}
          />
          <Route
            path="attendance/logs"
            element={<Navigate to="/dashboard/activities/logs" />}
          />
          <Route
            path="activities/teamsheets"
            element={<Navigate to="/dashboard/activities/hourmap" />}
          />
          <Route path="employees" element={<EmployeesPage />} />
          <Route path="employees/:id" element={<EmployeeDetailsPage />} />
          <Route path="leaves" element={<LeavesPage />} />
          <Route path="settings" element={<SettingsPage />} />
          <Route path="settings/company" element={<SettingsPage />} />
          <Route path="settings/profile" element={<SettingsPage />} />
          <Route path="settings/attendance" element={<SettingsPage />} />
          <Route path="settings/leave" element={<SettingsPage />} />
          <Route path="settings/projects" element={<SettingsPage />} />
          <Route
            path="settings/activities"
            element={<Navigate to="/dashboard/settings/projects" />}
          />
          <Route path="settings/notifications" element={<SettingsPage />} />
          <Route path="settings/integrations" element={<SettingsPage />} />
          <Route path="settings/billing" element={<SettingsPage />} />
        </Route>
      </Routes>
      </Router>
    </Provider>
  );
}

export default App;
