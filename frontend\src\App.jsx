import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Provider } from 'react-redux';
import { store } from './store';
import SignupPage from "./pages/SignupPage";
import LoginPage from "./pages/LoginPage";
import ForgotPasswordPage from "./pages/ForgotPasswordPage";
import ResetPasswordPage from "./pages/ResetPasswordPage";
import DashboardLayout from "./components/layout/DashboardLayout";
import DashboardPage from "./pages/dashboard/DashboardPage";
import ProjectsPage from "./pages/dashboard/ProjectsPage";
import ProjectDetailPage from "./pages/dashboard/ProjectDetailPage";
import PerformanceEvaluationsPage from "./pages/dashboard/PerformanceEvaluationsPage";
import EvaluationDetailsPage from "./pages/dashboard/EvaluationDetailsPage";
import AttendancePage from "./pages/dashboard/AttendancePage";
import CheckInLogsPage from "./pages/dashboard/attendance/CheckInLogsPage";
import HourMapPage from "./pages/activities/HourMapPage";
import EmployeesPage from "./pages/dashboard/EmployeesPage";
import EmployeeDetailsPage from "./pages/dashboard/EmployeeDetailsPage";
import LeavesPage from "./pages/dashboard/LeavesPage";
import SettingsPage from "./pages/dashboard/SettingsPage";
import IntegrationsPage from "./pages/dashboard/settings/IntegrationsPage";
import BillingPage from "./pages/dashboard/settings/BillingPage";
import LandingPage from "./pages/landing/LandingPage";

// Import landing page styles
import "./styles/landing.css";

// Onboarding pages
import EmailVerificationPage from "./pages/onboarding/EmailVerificationPage";
import CompanySetupPage from "./pages/onboarding/CompanySetupPage";
import InviteMembersPage from "./pages/onboarding/InviteMembersPage";

// protected route
import ProtectedRoute from "./components/auth/ProtectedRoute";

// Access control utilities
const isMaintenanceMode = () => {
  return process.env.REACT_APP_MAINTENANCE_MODE === 'true';
};

const isAdminAccess = () => {
  // Check for admin override via URL parameter
  const urlParams = new URLSearchParams(window.location.search);
  const adminKey = urlParams.get('admin');

  // Check for admin key in localStorage (persists across page reloads)
  const storedAdminKey = localStorage.getItem('adminAccess');

  // Replace 'your-secret-key' with your actual secret key
  const validAdminKey = process.env.REACT_APP_ADMIN_KEY || 'your-secret-key';

  if (adminKey === validAdminKey) {
    localStorage.setItem('adminAccess', 'true');
    return true;
  }

  return storedAdminKey === 'true';
};

// Component to handle restricted access
const RestrictedAccess = ({ children }) => {
  if (isMaintenanceMode() && !isAdminAccess()) {
    return <Navigate to="/" replace />;
  }
  return children;
};

function App() {
  return (
    <Provider store={store}>
      <Router>
      <Routes>
        {/* Landing page - always accessible */}
        <Route path="/" element={<LandingPage />} />

        {/* Auth routes - restricted in maintenance mode */}
        <Route path="/login" element={
          <RestrictedAccess>
            <LoginPage />
          </RestrictedAccess>
        } />
        <Route path="/signup" element={
          <RestrictedAccess>
            <SignupPage />
          </RestrictedAccess>
        } />
        <Route path="/forgot-password" element={
          <RestrictedAccess>
            <ForgotPasswordPage />
          </RestrictedAccess>
        } />
        <Route path="/reset-password" element={
          <RestrictedAccess>
            <ResetPasswordPage />
          </RestrictedAccess>
        } />

        {/* Onboarding routes - restricted in maintenance mode */}
        <Route path="/onboarding">
          <Route path="verify-email" element={
            <RestrictedAccess>
              <EmailVerificationPage />
            </RestrictedAccess>
          } />
          <Route path="company" element={
            <RestrictedAccess>
              <CompanySetupPage />
            </RestrictedAccess>
          } />
          <Route path="invite" element={
            <RestrictedAccess>
              <InviteMembersPage />
            </RestrictedAccess>
          } />
        </Route>

        {/* Dashboard routes - restricted in maintenance mode */}
        <Route path="/dashboard" element={
          <RestrictedAccess>
            <ProtectedRoute>
              <DashboardLayout />
            </ProtectedRoute>
          </RestrictedAccess>
          }
        >
          <Route index element={<DashboardPage />} />
          <Route path="projects" element={<ProjectsPage />} />
          <Route path="projects/:projectId" element={<ProjectDetailPage />} />
          {/* Activities main route */}
          <Route
            path="activities"
            element={<Navigate to="/dashboard/activities/overview" />}
          />
          <Route path="evaluations" element={<PerformanceEvaluationsPage />} />
          <Route path="evaluations/:id" element={<EvaluationDetailsPage />} />
          <Route path="attendance/overview" element={<AttendancePage />} />
          <Route path="attendance/logs" element={<CheckInLogsPage />} />

          {/* Activities routes */}
          <Route path="activities/overview" element={<AttendancePage />} />
          <Route path="activities/logs" element={<CheckInLogsPage />} />
          <Route path="activities/hourmap" element={<HourMapPage />} />

          {/* Legacy attendance routes - redirect to activities */}
          <Route
            path="attendance/overview"
            element={<Navigate to="/dashboard/activities/overview" />}
          />
          <Route
            path="attendance/logs"
            element={<Navigate to="/dashboard/activities/logs" />}
          />
          <Route
            path="activities/teamsheets"
            element={<Navigate to="/dashboard/activities/hourmap" />}
          />
          <Route path="employees" element={<EmployeesPage />} />
          <Route path="employees/:id" element={<EmployeeDetailsPage />} />
          <Route path="leaves" element={<LeavesPage />} />
          <Route path="settings" element={<SettingsPage />} />
          <Route path="settings/company" element={<SettingsPage />} />
          <Route path="settings/profile" element={<SettingsPage />} />
          <Route path="settings/attendance" element={<SettingsPage />} />
          <Route path="settings/leave" element={<SettingsPage />} />
          <Route path="settings/projects" element={<SettingsPage />} />
          <Route
            path="settings/activities"
            element={<Navigate to="/dashboard/settings/projects" />}
          />
          <Route path="settings/notifications" element={<SettingsPage />} />
          <Route path="settings/integrations" element={<SettingsPage />} />
          <Route path="settings/billing" element={<SettingsPage />} />
        </Route>
      </Routes>
      </Router>
    </Provider>
  );
}

export default App;
