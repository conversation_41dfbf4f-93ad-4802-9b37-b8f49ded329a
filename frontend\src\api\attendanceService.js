import axiosClient from './axiosClient';

const attendanceService = {
  // Get attendance records
  getAttendanceRecords: async (params = {}) => {
    try {
      const response = await axiosClient.get('/attendance', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Record check-in
  checkIn: async (checkInData) => {
    try {
      const response = await axiosClient.post('/attendance/check-in', checkInData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Record check-out
  checkOut: async (checkOutData) => {
    try {
      const response = await axiosClient.post('/attendance/check-out', checkOutData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get daily overview
  getDailyOverview: async (params = {}) => {
    try {
      const response = await axiosClient.get('/attendance/daily', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get detailed logs
  getDetailedLogs: async (params = {}) => {
    try {
      const response = await axiosClient.get('/attendance/logs', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get hours breakdown
  getHoursBreakdown: async (params = {}) => {
    try {
      const response = await axiosClient.get('/attendance/hourmap', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default attendanceService;
