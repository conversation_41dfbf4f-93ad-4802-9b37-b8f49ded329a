import axiosClient from './axiosClient';

const tasksService = {
  // Get all tasks
  getTasks: async () => {
    try {
      const response = await axiosClient.get('/tasks');
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Create new task
  createTask: async (taskData) => {
    try {
      const response = await axiosClient.post('/tasks', taskData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Get task by ID
  getTaskById: async (id) => {
    try {
      const response = await axiosClient.get(`/tasks/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update task
  updateTask: async (id, taskData) => {
    try {
      const response = await axiosClient.put(`/tasks/${id}`, taskData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Delete task
  deleteTask: async (id) => {
    try {
      const response = await axiosClient.delete(`/tasks/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Move task in Kanban
  moveTask: async (id, moveData) => {
    try {
      const response = await axiosClient.put(`/tasks/${id}/move`, moveData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Add comment to task
  addTaskComment: async (id, commentData) => {
    try {
      const response = await axiosClient.post(`/tasks/${id}/comments`, commentData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Add checklist item
  addChecklistItem: async (id, checklistData) => {
    try {
      const response = await axiosClient.post(`/tasks/${id}/checklist`, checklistData);
      return response;
    } catch (error) {
      throw error;
    }
  },
  
  // Update checklist item
  updateChecklistItem: async (id, itemId, updateData) => {
    try {
      const response = await axiosClient.put(`/tasks/${id}/checklist/${itemId}`, updateData);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default tasksService;
