import { X, Clock, Calendar, CheckCircle, AlertCircle, Activity, List, Filter, ArrowUpDown } from 'lucide-react';
import { useEffect, useState } from 'react';

const UserDetailsModal = ({ user, isOpen, onClose }) => {
  // State for active tab
  const [activeTab, setActiveTab] = useState('status');

  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  // Close modal when clicking outside
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Generate mock data for the user
  const generateMockData = () => {
    // Get current date
    const today = new Date();

    // Generate random check-in time (between 8:00 AM and 9:30 AM)
    const checkInHour = Math.floor(Math.random() * 2) + 8;
    const checkInMinute = Math.floor(Math.random() * 60);
    const checkInTime = `${checkInHour}:${checkInMinute.toString().padStart(2, '0')} ${checkInHour === 8 ? 'AM' : 'AM'}`;

    // Generate random check-out time (between 4:00 PM and 6:00 PM)
    const checkOutHour = Math.floor(Math.random() * 3) + 16;
    const checkOutMinute = Math.floor(Math.random() * 60);
    const checkOutTime = `${checkOutHour - 12}:${checkOutMinute.toString().padStart(2, '0')} PM`;

    // Generate random status
    const statuses = ['Active', 'On Leave', 'Remote'];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    // Generate random attendance rate
    const attendanceRate = Math.floor(Math.random() * 11) + 90; // 90-100%

    // Generate random leave days
    const leaveDaysUsed = Math.floor(Math.random() * 10);
    const leaveDaysRemaining = 20 - leaveDaysUsed;

    // Generate random recent activity
    const activities = [
      { date: new Date(today.getTime() - 1 * 24 * 60 * 60 * 1000), action: 'Checked in', time: '8:45 AM', message: 'Working on dashboard UI' },
      { date: new Date(today.getTime() - 1 * 24 * 60 * 60 * 1000), action: 'Checked out', time: '5:30 PM', message: 'Completed dashboard UI' },
      { date: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000), action: 'Checked in', time: '9:00 AM', message: 'Starting API integration' },
      { date: new Date(today.getTime() - 2 * 24 * 60 * 60 * 1000), action: 'Checked out', time: '6:00 PM', message: 'API integration in progress' },
      { date: new Date(today.getTime() - 3 * 24 * 60 * 60 * 1000), action: 'Requested leave', time: '10:30 AM', message: 'Family emergency' },
    ];

    // Generate detailed check-in/check-out logs for the past 30 days
    const logs = [];
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Skip weekends
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) continue;

      // Random chance for leave or absence
      const randomStatus = Math.random();
      if (randomStatus > 0.9) {
        // On leave
        logs.push({
          date: new Date(date),
          status: 'On Leave',
          checkIn: null,
          checkOut: null,
          duration: null,
          notes: 'Planned leave',
          location: null
        });
      } else if (randomStatus > 0.85) {
        // Absent
        logs.push({
          date: new Date(date),
          status: 'Absent',
          checkIn: null,
          checkOut: null,
          duration: null,
          notes: 'Unplanned absence',
          location: null
        });
      } else {
        // Regular day
        // Generate random check-in time (between 8:00 AM and 9:30 AM)
        const checkInHour = Math.floor(Math.random() * 2) + 8;
        const checkInMinute = Math.floor(Math.random() * 60);
        const checkInTime = new Date(date);
        checkInTime.setHours(checkInHour, checkInMinute, 0);

        // Generate random check-out time (between 4:00 PM and 6:30 PM)
        const checkOutHour = Math.floor(Math.random() * 3) + 16;
        const checkOutMinute = Math.floor(Math.random() * 60);
        const checkOutTime = new Date(date);
        checkOutTime.setHours(checkOutHour, checkOutMinute, 0);

        // Calculate duration in hours
        const durationMs = checkOutTime - checkInTime;
        const durationHours = Math.round((durationMs / (1000 * 60 * 60)) * 10) / 10;

        // Random location
        const locations = ['Office', 'Remote', 'Office', 'Office', 'Remote'];
        const location = locations[Math.floor(Math.random() * locations.length)];

        // Random notes (sometimes empty)
        const noteOptions = [
          '',
          '',
          'Team meeting',
          'Client call',
          'Project deadline',
          'Training session',
          'Department meeting',
          ''
        ];
        const notes = noteOptions[Math.floor(Math.random() * noteOptions.length)];

        logs.push({
          date: new Date(date),
          status: 'Present',
          checkIn: new Date(checkInTime),
          checkOut: new Date(checkOutTime),
          duration: durationHours,
          notes,
          location
        });
      }
    }

    // Sort logs by date (newest first)
    logs.sort((a, b) => b.date - a.date);

    return {
      checkInTime,
      checkOutTime,
      status,
      attendanceRate,
      leaveDaysUsed,
      leaveDaysRemaining,
      activities,
      logs
    };
  };

  // Get user's full name based on initials
  const getUserFullName = (initials) => {
    const names = {
      'JD': 'John Doe',
      'SW': 'Sarah Williams',
      'MJ': 'Mike Johnson',
      'JS': 'Jane Smith',
      'AK': 'Alex Kim',
      'RL': 'Rachel Lee',
      'TW': 'Tom Wilson',
      'BK': 'Brian Kim',
      'CP': 'Chris Park',
      'DM': 'David Miller'
    };

    return names[initials] || initials;
  };

  // Get user's job title based on initials
  const getUserJobTitle = (initials) => {
    const titles = {
      'JD': 'Senior UI Designer',
      'SW': 'Frontend Developer',
      'MJ': 'Backend Developer',
      'JS': 'Project Manager',
      'AK': 'UX Designer',
      'RL': 'QA Engineer',
      'TW': 'DevOps Engineer',
      'BK': 'Mobile Developer',
      'CP': 'Product Manager',
      'DM': 'Data Scientist'
    };

    return titles[initials] || 'Team Member';
  };

  // Get user color based on initials
  const getUserColor = (initials) => {
    const colors = {
      'JD': 'bg-soft-blue/20 text-soft-blue',
      'SW': 'bg-accent-2/20 text-accent-2',
      'MJ': 'bg-soft-purple/20 text-soft-purple',
      'JS': 'bg-accent-4/20 text-accent-4',
      'AK': 'bg-accent-1/20 text-accent-1',
      'RL': 'bg-accent-3/20 text-accent-3',
      'TW': 'bg-soft-teal/20 text-soft-teal',
      'BK': 'bg-soft-blue/20 text-soft-blue',
      'CP': 'bg-accent-2/20 text-accent-2',
      'DM': 'bg-soft-purple/20 text-soft-purple'
    };

    return colors[initials] || 'bg-surface-3/20 text-white';
  };

  // Generate mock data for the user
  const userData = generateMockData();

  return (
    <div className={`fixed inset-0 z-50 ${isOpen ? 'pointer-events-auto' : 'pointer-events-none'}`}>
      {/* Backdrop - only visible when modal is open */}
      <div
        className={` absolute inset-0 bg-black/50 transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0'}`}
        onClick={handleBackdropClick}
      ></div>

      {/* Sidebar that slides in from the right */}
      <div
        className={`
          fixed top-0 right-0 bottom-0
          w-[600px] max-w-[90%]
          bg-surface
          shadow-[0_0_20px_rgba(0,0,0,0.3)]
          transform
          transition-transform duration-300 ease-in-out
          flex flex-col
          z-[60]
          h-screen
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
      >
        {/* Header */}
        <div className="px-4 py-3 bg-surface-2 border-b border-border flex justify-between items-center">
          <h3 className="text-lg font-medium text-white">User Details</h3>
          <button
            onClick={onClose}
            className="text-text-secondary hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto flex-grow">
          {/* User info */}
          <div className="flex items-center mb-5">
            <div className={`h-12 w-12 rounded-full ${getUserColor(user).split(' ')[0]} flex items-center justify-center mr-3`}>
              <span className={`text-lg font-medium ${getUserColor(user).split(' ')[1]}`}>{user}</span>
            </div>
            <div>
              <h4 className="text-xl font-medium text-white">{getUserFullName(user)}</h4>
              <p className="text-text-secondary">{getUserJobTitle(user)}</p>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-border mb-4">
            <div className="flex">
              <button
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'status'
                    ? 'text-accent-2 border-b-2 border-accent-2'
                    : 'text-text-secondary hover:text-white'
                }`}
                onClick={() => setActiveTab('status')}
              >
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  Status
                </div>
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'attendance'
                    ? 'text-accent-1 border-b-2 border-accent-1'
                    : 'text-text-secondary hover:text-white'
                }`}
                onClick={() => setActiveTab('attendance')}
              >
                <div className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Attendance
                </div>
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'leave'
                    ? 'text-soft-purple border-b-2 border-soft-purple'
                    : 'text-text-secondary hover:text-white'
                }`}
                onClick={() => setActiveTab('leave')}
              >
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Leave
                </div>
              </button>
              <button
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'logs'
                    ? 'text-accent-3 border-b-2 border-accent-3'
                    : 'text-text-secondary hover:text-white'
                }`}
                onClick={() => setActiveTab('logs')}
              >
                <div className="flex items-center">
                  <List className="h-4 w-4 mr-2" />
                  Logs
                </div>
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="mb-5">
            {/* Today's Status Tab */}
            {activeTab === 'status' && (
              <div className="bg-surface-2/50 rounded-lg p-4">
                <h5 className="text-white font-medium mb-3">Today's Status</h5>
                <div className="space-y-2 text-white">
                  <p className="flex justify-between">
                    <span>Check-in:</span>
                    <span className="text-accent-2 font-medium">{userData.checkInTime}</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Check-out:</span>
                    <span className="text-accent-2 font-medium">{userData.checkOutTime}</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Working hours:</span>
                    <span className="text-accent-2 font-medium">8h 15m</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Status:</span>
                    <span className="text-accent-2 font-medium">{userData.status}</span>
                  </p>
                </div>
              </div>
            )}

            {/* Attendance Tab */}
            {activeTab === 'attendance' && (
              <div className="bg-surface-2/50 rounded-lg p-4">
                <h5 className="text-white font-medium mb-3">Attendance Summary</h5>
                <div className="space-y-2 text-white">
                  <p className="flex justify-between">
                    <span>Status:</span>
                    <span className="text-accent-1 font-medium">{userData.status}</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Attendance Rate:</span>
                    <span className="text-accent-1 font-medium">{userData.attendanceRate}%</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Days Present:</span>
                    <span className="text-accent-1 font-medium">19 days</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Late Check-ins:</span>
                    <span className="text-accent-1 font-medium">2 days</span>
                  </p>
                </div>
              </div>
            )}

            {/* Leave Tab */}
            {activeTab === 'leave' && (
              <div className="bg-surface-2/50 rounded-lg p-4">
                <h5 className="text-white font-medium mb-3">Leave Balance</h5>
                <div className="space-y-2 text-white">
                  <p className="flex justify-between">
                    <span>Used:</span>
                    <span className="text-soft-purple font-medium">{userData.leaveDaysUsed} days</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Remaining:</span>
                    <span className="text-soft-purple font-medium">{userData.leaveDaysRemaining} days</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Upcoming:</span>
                    <span className="text-soft-purple font-medium">2 days (Jul 15-16)</span>
                  </p>
                  <p className="flex justify-between">
                    <span>Last Leave:</span>
                    <span className="text-soft-purple font-medium">May 5-7</span>
                  </p>
                </div>
              </div>
            )}

            {/* Logs Tab */}
            {activeTab === 'logs' && (
              <div>
                {/* Filters and controls */}
                <div className="flex justify-between items-center mb-4">
                  <h5 className="text-white font-medium">Check-in/Check-out Logs</h5>
                  <div className="flex space-x-2">
                    <button className="px-2 py-1 bg-surface-2 rounded text-xs text-text-secondary hover:text-white flex items-center">
                      <Filter className="h-3 w-3 mr-1" />
                      Filter
                    </button>
                    <button className="px-2 py-1 bg-surface-2 rounded text-xs text-text-secondary hover:text-white flex items-center">
                      <ArrowUpDown className="h-3 w-3 mr-1" />
                      Sort
                    </button>
                  </div>
                </div>

                {/* Logs table */}
                <div className="bg-surface-2/30 rounded-lg overflow-hidden">
                  {/* Table header */}
                  <div className="grid grid-cols-12 gap-2 px-3 py-2 bg-surface-2/70 text-xs font-medium text-text-secondary">
                    <div className="col-span-2">Date</div>
                    <div className="col-span-2">Status</div>
                    <div className="col-span-2">Check-in</div>
                    <div className="col-span-2">Check-out</div>
                    <div className="col-span-1">Hours</div>
                    <div className="col-span-1">Location</div>
                    <div className="col-span-2">Notes</div>
                  </div>

                  {/* Table body */}
                  <div className="max-h-[400px] overflow-y-auto">
                    {userData.logs.map((log, index) => (
                      <div
                        key={index}
                        className={`grid grid-cols-12 gap-2 px-3 py-2 text-xs border-t border-border ${
                          index % 2 === 0 ? 'bg-surface-2/10' : 'bg-surface-2/20'
                        }`}
                      >
                        <div className="col-span-2 text-white">
                          {log.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}
                        </div>
                        <div className="col-span-2">
                          <span className={`px-1.5 py-0.5 rounded-full text-[10px] font-medium ${
                            log.status === 'Present' ? 'bg-accent-1/20 text-accent-1' :
                            log.status === 'On Leave' ? 'bg-soft-purple/20 text-soft-purple' :
                            'bg-accent-4/20 text-accent-4'
                          }`}>
                            {log.status}
                          </span>
                        </div>
                        <div className="col-span-2 text-text-secondary">
                          {log.checkIn ? log.checkIn.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : '-'}
                        </div>
                        <div className="col-span-2 text-text-secondary">
                          {log.checkOut ? log.checkOut.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : '-'}
                        </div>
                        <div className="col-span-1 text-text-secondary">
                          {log.duration ? `${log.duration}h` : '-'}
                        </div>
                        <div className="col-span-1 text-text-secondary">
                          {log.location || '-'}
                        </div>
                        <div className="col-span-2 text-text-secondary truncate">
                          {log.notes || '-'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Summary */}
                <div className="mt-4 bg-surface-2/50 rounded-lg p-3">
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Total Days</div>
                      <div className="text-white font-medium">{userData.logs.length} days</div>
                    </div>
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Avg. Hours</div>
                      <div className="text-white font-medium">
                        {(userData.logs
                          .filter(log => log.duration)
                          .reduce((sum, log) => sum + log.duration, 0) /
                          userData.logs.filter(log => log.duration).length
                        ).toFixed(1)}h
                      </div>
                    </div>
                    <div>
                      <div className="text-xs text-text-secondary mb-1">Attendance Rate</div>
                      <div className="text-white font-medium">
                        {Math.round(userData.logs.filter(log => log.status === 'Present').length / userData.logs.length * 100)}%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Recent Activity */}
          <div>
            <h5 className="text-white font-medium mb-3">Recent Activity</h5>
            <div className="space-y-3">
              {userData.activities.map((activity, index) => (
                <div key={index} className="bg-surface-2/30 rounded-lg p-3">
                  <div className="flex justify-between mb-1">
                    <span className="text-sm font-medium text-white">{activity.action}</span>
                    <span className="text-xs text-text-secondary">{activity.date.toLocaleDateString()} - {activity.time}</span>
                  </div>
                  {activity.message && (
                    <p className="text-sm text-text-secondary">{activity.message}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer */}
      </div>
    </div>
  );
};

export default UserDetailsModal;
