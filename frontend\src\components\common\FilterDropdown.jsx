import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { Filter, Calendar, ChevronDown, Check } from "lucide-react";
import DateRangePicker from "./DateRangePicker";
import {
  useFloating,
  offset,
  flip,
  shift,
  autoUpdate,
} from "@floating-ui/react-dom";

const FilterDropdown = ({
  options,
  defaultOption = "Today",
  onFilterChange,
  onDateRangeChange,
  className = "",
  showIcon = true,
  showDateRangePicker = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(defaultOption);
  const [showDateRange, setShowDateRange] = useState(false);

  // Setup Floating UI
  const { x, y, strategy, refs } = useFloating({
    placement: "bottom-start",
    middleware: [
      offset(5), // 5px distance from the reference
      flip(), // flip to the opposite side if no space
      shift({ padding: 8 }), // shift along the edge if needed
    ],
    whileElementsMounted: autoUpdate,
  });

  // Handle outside click
  useEffect(() => {
    if (!isOpen) return;

    const handleClickOutside = (event) => {
      const target = event.target;
      // Check if click is outside both the reference and floating elements
      if (
        refs.floating &&
        !refs.floating.contains(target) &&
        refs.reference &&
        !refs.reference.contains(target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, refs]);

  const handleOptionClick = (option) => {
    if (option === "Custom Range" && showDateRangePicker) {
      // Immediately show date range picker
      setSelectedOption(option);
      setShowDateRange(true);
      setIsOpen(false);
    } else {
      setSelectedOption(option);
      setIsOpen(false);
      setShowDateRange(false);
      if (onFilterChange) {
        onFilterChange(option);
      }
    }
  };

  // Toggle back to filter dropdown from date picker
  const toggleBackToDropdown = () => {
    setShowDateRange(false);
  };

  const handleDateRangeChange = (startDate, endDate) => {
    if (startDate && endDate) {
      const startFormatted = startDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
      const endFormatted = endDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });

      setSelectedOption(`${startFormatted} - ${endFormatted}`);

      if (onDateRangeChange) {
        onDateRangeChange(startDate, endDate);
      }
    } else {
      setSelectedOption(defaultOption);
      if (onFilterChange) {
        onFilterChange(defaultOption);
      }
    }
  };

  return (
    <div className={`relative ${className}`}>
      {showDateRange ? (
        <div className="flex flex-col">
          <div className="flex items-center mb-2">
            <button
              onClick={toggleBackToDropdown}
              className="text-sm text-accent-1 hover:text-accent-2 flex items-center"
            >
              <ChevronDown className="h-3 w-3 mr-1 rotate-90" />
              Back to filters
            </button>
          </div>
          <DateRangePicker
            onRangeChange={handleDateRangeChange}
            buttonClassName="z-20"
          />
        </div>
      ) : (
        <>
          <button
            ref={refs.setReference}
            onClick={() => setIsOpen(!isOpen)}
            className="px-3 py-1 btn-white-ghost text-sm rounded-lg flex items-center"
          >
            {showIcon &&
              (selectedOption === "Today" ||
              selectedOption === "Yesterday" ||
              selectedOption === "This Week" ||
              selectedOption === "This Month" ||
              selectedOption === "Last Month" ||
              selectedOption === "Custom Range" ||
              selectedOption.includes(" - ") ? (
                <Calendar className="h-4 w-4 inline mr-1" />
              ) : (
                <Filter className="h-4 w-4 inline mr-1" />
              ))}
            {selectedOption}
            <ChevronDown className="h-3 w-3 ml-1" />
          </button>

          {isOpen &&
            createPortal(
              <div
                ref={refs.setFloating}
                style={{
                  position: strategy,
                  top: y ?? 0,
                  left: x ?? 0,
                  width: "max-content",
                  zIndex: 9999, // Higher z-index to ensure it's above everything
                }}
                className="w-48 rounded-md shadow-lg bg-surface-2 border border-border"
              >
                <div className="py-1 max-h-60 overflow-auto scrollable-area">
                  {options.map((option) => (
                    <button
                      key={option}
                      className="w-full text-left px-4 py-2 text-sm hover:bg-surface-3 flex items-center justify-between"
                      onClick={() => handleOptionClick(option)}
                    >
                      <span className="text-white">{option}</span>
                      {selectedOption === option && (
                        <Check className="h-4 w-4 text-accent-1" />
                      )}
                    </button>
                  ))}

                  {showDateRangePicker && (
                    <button
                      className="w-full text-left px-4 py-2 text-sm hover:bg-surface-3 flex items-center justify-between"
                      onClick={() => handleOptionClick("Custom Range")}
                    >
                      <span className="text-white">Custom Range</span>
                      {selectedOption === "Custom Range" && (
                        <Check className="h-4 w-4 text-accent-1" />
                      )}
                    </button>
                  )}
                </div>
              </div>,
              document.body
            )}
        </>
      )}
    </div>
  );
};

export default FilterDropdown;
