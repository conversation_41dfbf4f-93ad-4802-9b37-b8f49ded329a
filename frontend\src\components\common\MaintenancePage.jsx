import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

const MaintenancePage = () => {
  const [adminKey, setAdminKey] = useState('');
  const [showAdminLogin, setShowAdminLogin] = useState(false);
  const navigate = useNavigate();

  const handleAdminLogin = (e) => {
    e.preventDefault();
    const validAdminKey = process.env.REACT_APP_ADMIN_KEY || 'your-secret-key';
    
    if (adminKey === validAdminKey) {
      localStorage.setItem('adminAccess', 'true');
      navigate('/dashboard');
    } else {
      alert('Invalid admin key');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background relative overflow-hidden">
      {/* Background elements for glassmorphism effect */}
      <div className="absolute w-72 h-72 rounded-full bg-soft-purple opacity-5 blur-3xl -top-20 -left-20"></div>
      <div className="absolute w-96 h-96 rounded-full bg-soft-teal opacity-5 blur-3xl -bottom-20 -right-20"></div>
      <div className="absolute w-64 h-64 rounded-full bg-soft-blue opacity-5 blur-3xl top-1/2 left-1/4"></div>

      <div className="glass-card p-8 w-full max-w-md z-10">
        <div className="text-center mb-8">
          <h1 className="text-2xl font-semibold text-white mb-2">
            🚧 Under Maintenance
          </h1>
          <p className="text-text-secondary text-sm">
            We're currently working on improvements. Please check back soon!
          </p>
        </div>

        {!showAdminLogin ? (
          <div className="text-center">
            <button
              onClick={() => setShowAdminLogin(true)}
              className="text-xs text-text-secondary hover:text-white transition-colors"
            >
              Admin Access
            </button>
          </div>
        ) : (
          <form onSubmit={handleAdminLogin} className="space-y-4">
            <div>
              <label htmlFor="adminKey" className="block text-sm font-medium text-white mb-1">
                Admin Key
              </label>
              <input
                id="adminKey"
                type="password"
                value={adminKey}
                onChange={(e) => setAdminKey(e.target.value)}
                className="input-dark block w-full px-3 py-2.5 rounded-lg text-white text-sm"
                placeholder="Enter admin key"
                required
              />
            </div>
            <button
              type="submit"
              className="w-full py-2.5 px-4 btn-white font-medium rounded-lg text-sm"
            >
              Access Admin Panel
            </button>
            <button
              type="button"
              onClick={() => setShowAdminLogin(false)}
              className="w-full py-2.5 px-4 btn-white-ghost font-medium rounded-lg text-sm"
            >
              Cancel
            </button>
          </form>
        )}
      </div>
    </div>
  );
};

export default MaintenancePage;
