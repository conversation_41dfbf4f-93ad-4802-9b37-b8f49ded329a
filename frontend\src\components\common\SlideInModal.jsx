import { useEffect } from 'react';
import { X } from 'lucide-react';

const SlideInModal = ({ 
  isOpen, 
  onClose, 
  title, 
  children,
  width = "600px",
  showCloseButton = true
}) => {
  // Close modal when Escape key is pressed
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
      // Prevent scrolling when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      // Restore scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [isOpen, onClose]);

  // Close modal when clicking outside
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className={`fixed inset-0 z-50 ${isOpen ? 'pointer-events-auto' : 'pointer-events-none'}`}>
      {/* Backdrop - only visible when modal is open */}
      <div
        className={`absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 ${isOpen ? 'opacity-100' : 'opacity-0'}`}
        onClick={handleBackdropClick}
      ></div>

      {/* Sidebar that slides in from the right */}
      <div
        className={`
          fixed top-0 right-0 bottom-0
          w-[${width}] max-w-[90%]
          bg-surface
          shadow-[0_0_20px_rgba(0,0,0,0.3)]
          transform
          transition-transform duration-300 ease-in-out
          flex flex-col
          z-[60]
          h-screen
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
        style={{ width }}
      >
        {/* Header */}
        <div className="px-4 py-3 bg-surface-2 border-b border-border flex justify-between items-center">
          <h3 className="text-lg font-medium text-white">{title}</h3>
          {showCloseButton && (
            <button
              onClick={onClose}
              className="text-text-secondary hover:text-white transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-4 overflow-y-auto flex-grow">
          {children}
        </div>
      </div>
    </div>
  );
};

export default SlideInModal;
