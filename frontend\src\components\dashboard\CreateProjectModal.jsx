import { useState } from "react";
import {
  Briefcase,
  Users,
  Calendar,
  AlignLeft,
  Check,
  ChevronDown,
  X,
} from "lucide-react";
import SlideInModal from "../common/SlideInModal";

const CreateProjectModal = ({ isOpen, onClose, onCreateProject }) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    dueDate: "",
    teamMembers: [], // Array of { id, role }
    projectFormat: "statuses", // 'statuses' or 'milestones'
  });

  const [showTeamDropdown, setShowTeamDropdown] = useState(false);
  const [selectedTeamMembers, setSelectedTeamMembers] = useState([]);

  // Mock team members for selection
  const teamMembers = [
    { id: 1, name: "<PERSON>", role: "Developer" },
    { id: 2, name: "<PERSON>", role: "Designer" },
    { id: 3, name: "<PERSON>", role: "Project Manager" },
    { id: 4, name: "<PERSON>", role: "Marketing" },
  ];

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle project format selection
  const handleFormatChange = (format) => {
    setFormData({
      ...formData,
      projectFormat: format,
    });
  };

  // Toggle team member selection
  const handleTeamMemberToggle = (memberId) => {
    setSelectedTeamMembers((prev) => {
      if (prev.includes(memberId)) {
        return prev.filter((id) => id !== memberId);
      } else {
        return [...prev, memberId];
      }
    });
  };

  // Add selected team members to the project
  const handleAddMembers = () => {
    const newTeamMembers = selectedTeamMembers.map((id) => ({
      id,
      role: teamMembers.find((m) => m.id === id)?.role || "Member",
    }));

    setFormData({
      ...formData,
      teamMembers: [...formData.teamMembers, ...newTeamMembers],
    });

    setSelectedTeamMembers([]);
    setShowTeamDropdown(false);
  };

  // Remove a team member from the project
  const handleRemoveMember = (memberId) => {
    setFormData({
      ...formData,
      teamMembers: formData.teamMembers.filter((m) => m.id !== memberId),
    });
  };

  // Get team member name by ID
  const getTeamMemberName = (id) => {
    return teamMembers.find((m) => m.id === id)?.name || "Unknown";
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Create a new project with default columns for Kanban
    const newProject = {
      ...formData,
      id: Date.now(),
      createdAt: new Date().toISOString(),
      tasks: [],
      columns: [
        { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
        { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
        { id: "completed", title: "Completed", color: "bg-green-500" },
      ],
      // If project format is milestones, add empty milestones array
      ...(formData.projectFormat === "milestones" && {
        milestones: [],
        milestoneProgress: 0,
      }),
    };

    onCreateProject(newProject);
    onClose();

    // Reset form
    setFormData({
      title: "",
      description: "",
      dueDate: "",
      teamMembers: [],
      projectFormat: "statuses",
    });
  };

  return (
    <SlideInModal
      isOpen={isOpen}
      onClose={onClose}
      title="Create New Project"
      width="600px"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* Project Title */}
          <div>
            <label
              htmlFor="title"
              className="block mb-1 text-sm font-medium text-white"
            >
              Project Title
            </label>
            <div className="relative">
              <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                <Briefcase className="w-5 h-5 text-text-secondary" />
              </div>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Enter project title"
                required
              />
            </div>
          </div>

          {/* Project Description */}
          <div>
            <label
              htmlFor="description"
              className="block mb-1 text-sm font-medium text-white"
            >
              Description
            </label>
            <div className="relative">
              <div className="flex absolute left-0 top-3 items-start pl-3 pointer-events-none">
                <AlignLeft className="w-5 h-5 text-text-secondary" />
              </div>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="3"
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Enter project description"
              ></textarea>
            </div>
          </div>

          {/* Due Date */}
          <div>
            <label
              htmlFor="dueDate"
              className="block mb-1 text-sm font-medium text-white"
            >
              Due Date
            </label>
            <div className="relative">
              <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                <Calendar className="w-5 h-5 text-text-secondary" />
              </div>
              <input
                type="date"
                id="dueDate"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              />
            </div>
          </div>

          {/* Project Format */}
          <div>
            <label className="block mb-1 text-sm font-medium text-white">
              Project Format
            </label>
            <div className="flex space-x-3">
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium ${
                  formData.projectFormat === "statuses"
                    ? "bg-violet-600 text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => handleFormatChange("statuses")}
              >
                Status Columns
              </button>
              <button
                type="button"
                className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium ${
                  formData.projectFormat === "milestones"
                    ? "bg-indigo-600 text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => handleFormatChange("milestones")}
              >
                Milestones
              </button>
            </div>
          </div>

          {/* Team Members */}
          <div>
            <label className="block mb-1 text-sm font-medium text-white">
              Team Members
            </label>
            <div className="relative">
              <button
                type="button"
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 w-full p-2.5 flex items-center justify-between"
                onClick={() => setShowTeamDropdown(!showTeamDropdown)}
              >
                <div className="flex items-center">
                  <Users className="mr-2 w-5 h-5 text-text-secondary" />
                  <span>
                    {formData.teamMembers.length > 0
                      ? `${formData.teamMembers.length} members selected`
                      : "Add team members"}
                  </span>
                </div>
                <ChevronDown className="w-4 h-4 text-text-secondary" />
              </button>

              {/* Team members dropdown */}
              {showTeamDropdown && (
                <div className="absolute z-10 mt-1 w-full rounded-lg border shadow-lg bg-surface-3 border-border">
                  <div className="overflow-y-auto p-2 max-h-60">
                    {teamMembers.map((member) => (
                      <div
                        key={member.id}
                        className="flex items-center p-2 rounded-lg cursor-pointer hover:bg-surface-2"
                        onClick={() => handleTeamMemberToggle(member.id)}
                      >
                        <input
                          type="checkbox"
                          className="mr-2"
                          checked={selectedTeamMembers.includes(member.id)}
                          onChange={() => {}}
                        />
                        <div>
                          <p className="text-sm text-white">{member.name}</p>
                          <p className="text-xs text-text-secondary">
                            {member.role}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-end p-2 border-t border-border">
                    <button
                      type="button"
                      className="px-3 py-1 text-xs text-white rounded-lg bg-surface-3 hover:bg-surface"
                      onClick={handleAddMembers}
                    >
                      Add Selected
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end mt-6 space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium rounded-lg btn-white-ghost"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 text-sm font-medium rounded-lg btn-white"
          >
            Create Project
          </button>
        </div>
      </form>
    </SlideInModal>
  );
};

export default CreateProjectModal;
