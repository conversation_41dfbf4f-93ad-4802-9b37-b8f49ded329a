import { useState } from 'react';
import { AlignLeft, Calendar, Users, Tag, CheckSquare, Plus, X } from 'lucide-react';
import SlideInModal from '../common/SlideInModal';

const CreateTaskModal = ({ isOpen, onClose, onCreateTask, projectId, columnId, teamMembers }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    dueDate: '',
    assignees: [],
    labels: [],
    checklist: []
  });

  // Available label colors
  const labelColors = [
    { name: 'Red', class: 'bg-red-500 text-white' },
    { name: 'Green', class: 'bg-green-500 text-white' },
    { name: 'Blue', class: 'bg-blue-500 text-white' },
    { name: 'Yellow', class: 'bg-yellow-500 text-white' },
    { name: 'Purple', class: 'bg-purple-500 text-white' }
  ];

  // New checklist item and label
  const [newChecklistItem, setNewChecklistItem] = useState('');
  const [newLabelName, setNewLabelName] = useState('');
  const [selectedLabelColor, setSelectedLabelColor] = useState(labelColors[0].class);
  const [showLabelForm, setShowLabelForm] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleAssigneeToggle = (memberId) => {
    setFormData(prev => {
      const isSelected = prev.assignees.includes(memberId);
      return {
        ...prev,
        assignees: isSelected
          ? prev.assignees.filter(id => id !== memberId)
          : [...prev.assignees, memberId]
      };
    });
  };

  const addChecklistItem = () => {
    if (newChecklistItem.trim() === '') return;
    
    setFormData(prev => ({
      ...prev,
      checklist: [
        ...prev.checklist,
        { id: Date.now(), text: newChecklistItem, completed: false }
      ]
    }));
    
    setNewChecklistItem('');
  };

  const removeChecklistItem = (itemId) => {
    setFormData(prev => ({
      ...prev,
      checklist: prev.checklist.filter(item => item.id !== itemId)
    }));
  };

  const addLabel = () => {
    if (newLabelName.trim() === '') return;
    
    setFormData(prev => ({
      ...prev,
      labels: [
        ...prev.labels,
        { id: Date.now(), name: newLabelName, color: selectedLabelColor }
      ]
    }));
    
    setNewLabelName('');
    setShowLabelForm(false);
  };

  const removeLabel = (labelId) => {
    setFormData(prev => ({
      ...prev,
      labels: prev.labels.filter(label => label.id !== labelId)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Create a new task
    const newTask = {
      ...formData,
      id: Date.now().toString(),
      projectId,
      status: columnId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      createdAt: new Date().toISOString()
    };
    
    onCreateTask(newTask);
    onClose();
    
    // Reset form
    setFormData({
      title: '',
      description: '',
      dueDate: '',
      assignees: [],
      labels: [],
      checklist: []
    });
  };

  return (
    <SlideInModal
      isOpen={isOpen}
      onClose={onClose}
      title="Create New Task"
      width="600px"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* Task Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-white mb-1">
              Task Title
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
              placeholder="Enter task title"
              required
            />
          </div>

          {/* Task Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-white mb-1">
              Description
            </label>
            <div className="relative">
              <div className="absolute top-3 left-3 pointer-events-none">
                <AlignLeft className="h-5 w-5 text-text-secondary" />
              </div>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="3"
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Enter task description"
              ></textarea>
            </div>
          </div>

          {/* Due Date */}
          <div>
            <label htmlFor="dueDate" className="block text-sm font-medium text-white mb-1">
              Due Date
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-text-secondary" />
              </div>
              <input
                type="date"
                id="dueDate"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              />
            </div>
          </div>

          {/* Assignees */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Assignees
            </label>
            <div className="bg-surface-3 border border-border rounded-lg p-3 max-h-32 overflow-y-auto">
              {teamMembers && teamMembers.map(member => (
                <div 
                  key={member.id}
                  className="flex items-center mb-2 last:mb-0"
                >
                  <input
                    type="checkbox"
                    id={`assignee-${member.id}`}
                    checked={formData.assignees.includes(member.id)}
                    onChange={() => handleAssigneeToggle(member.id)}
                    className="w-4 h-4 text-accent-1 bg-surface border-border rounded focus:ring-accent-1"
                  />
                  <label
                    htmlFor={`assignee-${member.id}`}
                    className="ml-2 text-sm text-white flex items-center justify-between w-full"
                  >
                    <span>{member.name}</span>
                    <span className="text-xs text-text-secondary">{member.role}</span>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Labels */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Labels
            </label>
            <div className="bg-surface-3 border border-border rounded-lg p-3">
              {/* Existing labels */}
              <div className="flex flex-wrap gap-2 mb-3">
                {formData.labels.map(label => (
                  <div 
                    key={label.id}
                    className={`px-2 py-1 rounded-full text-xs flex items-center ${label.color}`}
                  >
                    <span>{label.name}</span>
                    <button 
                      type="button"
                      onClick={() => removeLabel(label.id)}
                      className="ml-1 hover:text-red-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </div>
                ))}
              </div>
              
              {/* Add label form */}
              {showLabelForm ? (
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      type="text"
                      value={newLabelName}
                      onChange={(e) => setNewLabelName(e.target.value)}
                      placeholder="Label name"
                      className="bg-surface border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2 mr-2"
                    />
                    <button
                      type="button"
                      onClick={addLabel}
                      className="p-2 bg-surface-2 hover:bg-surface text-white rounded-lg"
                    >
                      <Plus className="h-5 w-5" />
                    </button>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    {labelColors.map(color => (
                      <button
                        key={color.name}
                        type="button"
                        className={`w-6 h-6 rounded-full ${color.class} ${selectedLabelColor === color.class ? 'ring-2 ring-white' : ''}`}
                        onClick={() => setSelectedLabelColor(color.class)}
                      ></button>
                    ))}
                  </div>
                  
                  <button
                    type="button"
                    className="text-xs text-text-secondary hover:text-white"
                    onClick={() => setShowLabelForm(false)}
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <button
                  type="button"
                  onClick={() => setShowLabelForm(true)}
                  className="flex items-center text-sm text-text-secondary hover:text-white"
                >
                  <Tag className="h-4 w-4 mr-1" />
                  <span>Add Label</span>
                </button>
              )}
            </div>
          </div>

          {/* Checklist */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Checklist
            </label>
            <div className="bg-surface-3 border border-border rounded-lg p-3">
              {/* Existing checklist items */}
              <div className="space-y-2 mb-3">
                {formData.checklist.map(item => (
                  <div 
                    key={item.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center">
                      <CheckSquare className="h-4 w-4 text-text-secondary mr-2" />
                      <span className="text-sm text-white">{item.text}</span>
                    </div>
                    <button 
                      type="button"
                      onClick={() => removeChecklistItem(item.id)}
                      className="text-text-secondary hover:text-red-400"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
              
              {/* Add checklist item */}
              <div className="flex items-center">
                <input
                  type="text"
                  value={newChecklistItem}
                  onChange={(e) => setNewChecklistItem(e.target.value)}
                  placeholder="Add checklist item"
                  className="bg-surface border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2 mr-2"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addChecklistItem())}
                />
                <button
                  type="button"
                  onClick={addChecklistItem}
                  className="p-2 bg-surface-2 hover:bg-surface text-white rounded-lg"
                >
                  <Plus className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
          >
            Create Task
          </button>
        </div>
      </form>
    </SlideInModal>
  );
};

export default CreateTaskModal;
