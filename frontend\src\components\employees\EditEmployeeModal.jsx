import { useState, useEffect } from "react";
import { User, Mail, Briefcase, Users, Calendar } from "lucide-react";
import Modal from "../common/Modal";

const EditEmployeeModal = ({ isOpen, onClose, employee, onSave }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    position: "",
    department: "",
    role: "",
    dateJoined: "",
    bio: ""
  });

  useEffect(() => {
    if (employee) {
      setFormData({
        name: employee.name || "",
        email: employee.email || "",
        position: employee.position || "",
        department: employee.department || "",
        role: employee.role || "",
        dateJoined: employee.dateJoined || "",
        bio: employee.bio || ""
      });
    }
  }, [employee]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave({ ...employee, ...formData });
    onClose();
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      title="Edit Employee" 
      size="lg"
    >
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">
              Full Name
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-4 w-4 text-text-secondary" />
              </div>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="John Doe"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">
              Email
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Mail className="h-4 w-4 text-text-secondary" />
              </div>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">
              Position
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Briefcase className="h-4 w-4 text-text-secondary" />
              </div>
              <input
                type="text"
                name="position"
                value={formData.position}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Developer"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">
              Department
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Users className="h-4 w-4 text-text-secondary" />
              </div>
              <input
                type="text"
                name="department"
                value={formData.department}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Engineering"
                required
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">
              Role
            </label>
            <select
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
              required
            >
              <option value="Admin">Admin</option>
              <option value="Manager">Manager</option>
              <option value="User">User</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-text-secondary mb-1">
              Date Joined
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-4 w-4 text-text-secondary" />
              </div>
              <input
                type="date"
                name="dateJoined"
                value={formData.dateJoined}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                required
              />
            </div>
          </div>
        </div>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-text-secondary mb-1">
            Bio
          </label>
          <textarea
            name="bio"
            value={formData.bio}
            onChange={handleInputChange}
            rows="4"
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
            placeholder="Brief description about the employee..."
          ></textarea>
        </div>
        
        <div className="flex justify-end space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
          >
            Save Changes
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default EditEmployeeModal;
