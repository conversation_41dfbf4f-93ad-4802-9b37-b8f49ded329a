import { useState } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import TopNav from './TopNav';
import UserDetailsModal from '../attendance/UserDetailsModal';
import { UserDetailsProvider, useUserDetails } from '../../context/UserDetailsContext';

// Inner component that uses the context
const DashboardContent = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { selectedUser, isModalOpen, closeUserModal } = useUserDetails();

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      {/* Sidebar */}
      <Sidebar sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Navigation */}
        <TopNav sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        {/* Page content */}
        <main className="flex-1 overflow-y-auto bg-surface p-4 md:p-6">
          <Outlet />
        </main>
      </div>
      <UserDetailsModal
          user={selectedUser}
          isOpen={isModalOpen}
          onClose={closeUserModal}
        />
    </div>
  );
};

// Wrapper component that provides the context
const DashboardLayout = () => {
  return (
    <UserDetailsProvider>
      <DashboardContent />
    </UserDetailsProvider>
  );
};

export default DashboardLayout;
