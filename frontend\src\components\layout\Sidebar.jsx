import { useEffect, useState, useRef } from "react";
import { NavLink, useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  CheckSquare,
  Users,
  Calendar,
  Settings,
  X,
  Clock,
  ChevronRight,
  ChevronDown,
  ChevronUp,
  Menu,
  Star,
  PanelLeftOpen,
  PanelLeftClose,
  UserCog,
  Building2,
  Bell,
  CreditCard,
  Briefcase,
  Cog,
  ChevronsUpDown,
} from "lucide-react";

const Sidebar = ({ sidebarOpen, setSidebarOpen }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [openMenus, setOpenMenus] = useState({
    activities: false,
    employees: false,
    projectactivities: false,
    evaluations: false,
  });

  // State for settings popover
  const [showSettingsPopover, setShowSettingsPopover] = useState(false);
  const settingsPopoverRef = useRef(null);

  // Use location to track route changes
  const location = useLocation();

  // Force re-render when location changes
  const [, setForceUpdate] = useState(0);

  // Effect to handle route changes
  useEffect(() => {
    // Force component to re-render when route changes
    setForceUpdate((prev) => prev + 1);

    // Auto-expand the menu for the current route
    const currentPath = location.pathname;

    navigation.forEach((item) => {
      if (
        item.hasChildren &&
        item.children.some((child) => currentPath === child.href)
      ) {
        // Auto-expand this menu
        setOpenMenus((prev) => ({
          ...prev,
          [item.name.toLowerCase().replace(" ", "")]: true,
        }));
      }
    });

    // Close settings popover when route changes
    setShowSettingsPopover(false);
  }, [location.pathname]);

  // Handle click outside for settings popover
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        settingsPopoverRef.current &&
        !settingsPopoverRef.current.contains(event.target)
      ) {
        setShowSettingsPopover(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleMenu = (menu) => {
    setOpenMenus((prev) => ({
      ...prev,
      [menu]: !prev[menu],
    }));
  };

  // Navigation items with children
  const navigation = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
      exact: true,
    },
    {
      name: "Projects",
      href: "/dashboard/projects",
      icon: CheckSquare,
      hasChildren: false,
    },
    {
      name: "Performance Evaluations",
      href: "/dashboard/evaluations",
      icon: Star,
      hasChildren: false,
    },
    {
      name: "Activities",
      href: "/dashboard/activities",
      icon: Clock,
      hasChildren: true,
      children: [
        { name: "Daily Overview", href: "/dashboard/activities/overview" },
        { name: "Check-in/out Logs", href: "/dashboard/activities/logs" },
        { name: "HourMap", href: "/dashboard/activities/hourmap" },
      ],
    },
    {
      name: "Employees",
      href: "/dashboard/employees",
      icon: Users,
    },
    {
      name: "Leave Management",
      href: "/dashboard/leaves",
      icon: Calendar,
    },
    {
      name: "Settings",
      href: "/dashboard/settings",
      icon: Settings,
      // hasChildren: true,
      // children: [
      //   { name: "General", href: "/dashboard/settings" },
      //   { name: "Company Profile", href: "/dashboard/settings/company" },
      //   { name: "Personal Profile", href: "/dashboard/settings/profile" },
      //   { name: "Attendance", href: "/dashboard/settings/attendance" },
      //   { name: "Leave Management", href: "/dashboard/settings/leave" },
      //   { name: "Projects", href: "/dashboard/settings/projects" },
      //   { name: "Notifications", href: "/dashboard/settings/notifications" },
      //   { name: "Integrations", href: "/dashboard/settings/integrations" },
      //   { name: "Billing", href: "/dashboard/settings/billing" },
      // ],
    },
  ];

  // Render a nav item with children
  const renderNavItem = (item) => {
    const isMenuOpen =
      item.hasChildren && openMenus[item.name.toLowerCase().replace(" ", "")];

    // Get current path from React Router's location hook
    const currentPath = location.pathname;

    // Special case for Dashboard - only active when exactly at /dashboard
    if (item.name === "Dashboard") {
      // Dashboard should only be active when exactly at /dashboard
      const isActive = currentPath === "/dashboard";
      return renderWithActiveState(item, isActive, isMenuOpen);
    }

    // For all other items
    let isActive = false;

    if (item.hasChildren) {
      // For parent items with children, they're active if any child is active
      isActive = item.children.some((child) => currentPath === child.href);
    } else {
      // For items without children, they're active only on exact match
      isActive = currentPath === item.href;
    }

    return renderWithActiveState(item, isActive, isMenuOpen);
  };

  // Helper function to render with active state
  const renderWithActiveState = (item, isActive, isMenuOpen) => {
    return (
      <div key={item.name} className="mb-1">
        {item.hasChildren ? (
          <>
            <button
              onClick={() =>
                toggleMenu(item.name.toLowerCase().replace(" ", ""))
              }
              className={`w-full flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? "bg-surface text-white"
                  : "text-text-secondary hover:text-white hover:bg-surface"
              }`}
            >
              <div className="flex items-center">
                <item.icon
                  size={collapsed ? 20 : 18}
                  className={collapsed ? "mx-auto" : "flex-shrink-0 mr-3"}
                />
                {!collapsed && <span>{item.name}</span>}
              </div>
              {!collapsed &&
                (isMenuOpen ? (
                  <ChevronDown size={16} />
                ) : (
                  <ChevronRight size={16} />
                ))}
            </button>

            {/* Children items */}
            {isMenuOpen && !collapsed && (
              <div className="pl-3 mt-1 ml-7 space-y-1 border-l border-border">
                {item.children.map((child) => (
                  <NavLink
                    key={child.name}
                    to={child.href}
                    end={true} /* Force exact matching */
                    className={({ isActive }) =>
                      `block px-3 py-2 text-sm transition-colors ${
                        isActive
                          ? "text-white font-medium"
                          : "text-text-secondary hover:text-white"
                      }`
                    }
                  >
                    {child.name}
                  </NavLink>
                ))}
              </div>
            )}
          </>
        ) : (
          <NavLink
            to={item.href}
            end={true} /* Force exact matching */
            className={({ isActive }) =>
              `flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors ${
                isActive
                  ? "bg-surface text-white"
                  : "text-text-secondary hover:text-white hover:bg-surface"
              }`
            }
          >
            <item.icon
              size={collapsed ? 20 : 18}
              className={collapsed ? "mx-auto" : "flex-shrink-0 mr-3"}
            />
            {!collapsed && item.name}
          </NavLink>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile sidebar backdrop */}
      <div
        className={`fixed inset-0 z-40 md:hidden bg-black bg-opacity-50 transition-opacity duration-300 ease-in-out ${
          sidebarOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={() => setSidebarOpen(false)}
      ></div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-40 ${
          collapsed ? "w-16" : "w-64"
        } bg-surface-3 transition-all duration-300 ease-in-out transform ${
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        } md:translate-x-0 md:static md:z-auto`}
      >
        {/* Sidebar header */}
        <div className="flex justify-between items-center px-4 h-[86px] border-b border-border">
          {!collapsed ? (
            <>
              <div className="flex items-center">
                <img
                  src="/logo5.png"
                  alt="TeamCheck Logo"
                  className="mr-2 w-[36px] rounded-md h-auto object-cover"
                />
                <h1 className="text-xl font-semibold text-white">TeamCheck</h1>
              </div>
              <div className="flex items-center space-x-2">
                {/* Collapse toggle button */}
                <button
                  type="button"
                  onClick={() => setCollapsed(true)}
                  className="hidden p-1 rounded-md md:block text-text-secondary hover:text-white"
                >
                  <PanelLeftClose size={18} />
                </button>
                {/* Mobile close button */}
                <button
                  type="button"
                  className="md:hidden text-text-secondary hover:text-white"
                  onClick={() => setSidebarOpen(false)}
                >
                  <X size={20} />
                </button>
              </div>
            </>
          ) : (
            <div className="flex justify-center items-center w-full">
              {/* Only show menu button when collapsed */}
              <button
                type="button"
                className="p-1 rounded-md text-text-secondary hover:text-white"
                onClick={() => setCollapsed(false)}
              >
                <PanelLeftOpen size={18} />
              </button>
            </div>
          )}
        </div>

        {/* Sidebar content */}
        <div className="py-4 overflow-y-auto h-[calc(100%-8rem)]">
          <nav className={`space-y-1 ${collapsed ? "px-1" : "px-2"}`}>
            {navigation.map(renderNavItem)}
          </nav>
        </div>

        {/* Sidebar footer */}
        <div className="absolute bottom-0 p-4 w-full border-t border-border bg-surface-3">
          {collapsed ? (
            <div className="flex justify-center">
              <img
                src="/logo5.png"
                alt="Organization Logo"
                className="w-[36px] rounded-md h-auto object-cover"
              />
            </div>
          ) : (
            <div className="flex relative justify-between items-center">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <img
                    src="/logo5.png"
                    alt="Organization Logo"
                    className="w-[36px] rounded-md h-auto object-cover"
                  />
                </div>
                <div className="ml-3 bg-surface-3">
                  <p className="text-sm font-medium text-white">
                    Your Organization
                  </p>
                  <p className="text-xs text-text-secondary">
                    TeamCheck v1.0.0
                  </p>
                </div>
              </div>

              {/* Settings button */}
              <button
                onClick={() => setShowSettingsPopover(!showSettingsPopover)}
                className="p-1 rounded-md text-text-secondary hover:text-white"
              >
                <ChevronsUpDown className="w-5 h-5" />
              </button>

              {/* Settings popover */}
              {showSettingsPopover && (
                <div
                  ref={settingsPopoverRef}
                  className="absolute right-0 bottom-16 z-50 p-2 w-[180px] rounded-lg border shadow-lg bg-surface-2 border-border"
                >
                  <div className="flex flex-col gap-1">
                    <NavLink
                      to="/dashboard/settings"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <Building2 className="mr-2 w-4 h-4" />
                      <span>Company</span>
                    </NavLink>

                    <NavLink
                      to="/dashboard/settings/profile"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <UserCog className="mr-2 w-4 h-4" />
                      <span>Profile</span>
                    </NavLink>

                    <NavLink
                      to="/dashboard/settings/attendance"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <Clock className="mr-2 w-4 h-4" />
                      <span>Attendance</span>
                    </NavLink>

                    <NavLink
                      to="/dashboard/settings/leave"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <Calendar className="mr-2 w-4 h-4" />
                      <span>Leave</span>
                    </NavLink>

                    <NavLink
                      to="/dashboard/settings/projects"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <Briefcase className="mr-2 w-4 h-4" />
                      <span>Projects</span>
                    </NavLink>

                    <NavLink
                      to="/dashboard/settings/notifications"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <Bell className="mr-2 w-4 h-4" />
                      <span>Notifications</span>
                    </NavLink>

                    <NavLink
                      to="/dashboard/settings/billing"
                      className={({ isActive }) =>
                        `flex items-center p-2 text-sm rounded-md ${
                          isActive
                            ? "bg-surface text-white"
                            : "text-text-secondary hover:text-white hover:bg-surface"
                        }`
                      }
                      onClick={() => setShowSettingsPopover(false)}
                    >
                      <CreditCard className="mr-2 w-4 h-4" />
                      <span>Billing</span>
                    </NavLink>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Sidebar;
