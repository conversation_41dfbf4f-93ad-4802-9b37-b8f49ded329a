import { useState, useRef, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { logout } from "../../store/slices/authSlice";
import {
  Menu,
  Bell,
  User,
  LogOut,
  Settings,
  ChevronDown,
  ChevronRight,
  LayoutDashboard,
  CheckSquare,
  Clock,
  Users,
  Calendar,
  Star,
} from "lucide-react";

const TopNav = ({ sidebarOpen, setSidebarOpen }) => {
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const userMenuRef = useRef(null);
  const notificationsRef = useRef(null);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // Get user data from Redux store
  const { user } = useAppSelector((state) => state.auth);

  // Handle logout
  const handleLogout = () => {
    // Show confirmation dialog
    dispatch(logout());

    // Close user menu
    setUserMenuOpen(false);

    // Navigate to login page
    navigate('/login');
    
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user?.name) return 'U';
    const names = user.name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return names[0][0].toUpperCase();
  };

  // Navigation structure - should match sidebar
  const navigationStructure = [
    {
      name: "Dashboard",
      path: "/dashboard",
      icon: LayoutDashboard,
      children: [],
    },
    {
      name: "Projects",
      path: "/dashboard/projects",
      icon: CheckSquare,
      children: [],
    },
    {
      name: "Performance Evaluations",
      path: "/dashboard/evaluations",
      icon: Star,
      children: [],
    },
    {
      name: "Activities",
      path: "/dashboard/activities",
      icon: Clock,
      children: [
        { name: "Daily Overview", path: "/dashboard/activities" },
        { name: "Check-in/out Logs", path: "/dashboard/activities/logs" },
        { name: "HourMap", path: "/dashboard/activities/hourmap" },
      ],
    },
    {
      name: "Employees",
      path: "/dashboard/employees",
      icon: Users,
      children: [
        { name: "All Employees", path: "/dashboard/employees" },
        { name: "Performance", path: "/dashboard/employees/performance" },
        { name: "Roles & Permissions", path: "/dashboard/employees/roles" },
      ],
    },
    {
      name: "Leave Management",
      path: "/dashboard/leaves",
      icon: Calendar,
      children: [],
    },
    {
      name: "Settings",
      path: "/dashboard/settings",
      icon: Settings,
      children: [
        { name: "General", path: "/dashboard/settings" },
        { name: "Company Profile", path: "/dashboard/settings/company" },
        { name: "Notifications", path: "/dashboard/settings/notifications" },
        { name: "Integrations", path: "/dashboard/settings/integrations" },
      ],
    },
  ];

  // Generate breadcrumbs based on current path
  const getBreadcrumbs = () => {
    const currentPath = location.pathname;
    const breadcrumbs = [];

    // Special case for dashboard home
    if (currentPath === "/dashboard") {
      return [
        {
          name: "Dashboard",
          path: "/dashboard",
          icon: LayoutDashboard,
        },
      ];
    }

    // For all other paths, find the matching section
    for (const section of navigationStructure) {
      // Skip the Dashboard section for other routes
      if (section.path === "/dashboard") {
        continue;
      }

      // Check if we're in this section
      if (
        currentPath === section.path ||
        currentPath.startsWith(`${section.path}/`)
      ) {
        // Add the main section
        breadcrumbs.push({
          name: section.name,
          path: section.path,
          icon: section.icon,
        });

        // If we're in a subsection, add it too
        if (currentPath !== section.path && section.children.length > 0) {
          const subSection = section.children.find(
            (child) => child.path === currentPath
          );
          if (subSection) {
            breadcrumbs.push({
              name: subSection.name,
              path: subSection.path,
            });
          }
        }

        // We found the section, no need to continue
        break;
      }
    }

    return breadcrumbs;
  };

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setUserMenuOpen(false);
      }
      if (
        notificationsRef.current &&
        !notificationsRef.current.contains(event.target)
      ) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Mock notifications
  const notifications = [
    {
      id: 1,
      message: "John Doe requested leave",
      time: "10 minutes ago",
      read: false,
    },
    {
      id: 2,
      message: "New activity assigned: Website Redesign",
      time: "1 hour ago",
      read: false,
    },
    {
      id: 3,
      message: "Team meeting at 2:00 PM",
      time: "3 hours ago",
      read: true,
    },
    {
      id: 3,
      message: "Team meeting at 2:00 PM",
      time: "3 hours ago",
      read: false,
    },
    {
      id: 3,
      message: "Team meeting at 2:00 PM",
      time: "3 hours ago",
      read: true,
    },
  ];

  return (
    <header className="border-b bg-surface-2 border-border">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-[86px]">
          {/* Left: Hamburger menu (mobile) and breadcrumb */}
          <div className="flex items-center">
            <button
              type="button"
              className="mr-3 md:hidden text-text-secondary hover:text-white focus:outline-none"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <Menu size={20} />
            </button>
            <div>
              {/* Breadcrumbs */}
              <div className="flex items-center">
                {getBreadcrumbs().map((crumb, index) => (
                  <div key={crumb.path} className="flex items-center">
                    {index > 0 && (
                      <ChevronRight
                        size={16}
                        className="mx-2 text-text-secondary"
                      />
                    )}
                    <Link
                      to={crumb.path}
                      className={`flex items-center ${
                        index === getBreadcrumbs().length - 1
                          ? "text-white font-semibold"
                          : "text-text-secondary hover:text-white"
                      }`}
                    >
                      {/* No icons in breadcrumbs */}
                      <span className={index === 0 ? "text-lg" : "text-sm"}>
                        {crumb.name}
                      </span>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right: Notifications and user menu */}
          <div className="flex items-center space-x-4">

            {/* Notifications */}
            <div className="relative" ref={notificationsRef}>
              <button
                type="button"
                className="relative text-text-secondary hover:text-white focus:outline-none"
                onClick={() => setNotificationsOpen(!notificationsOpen)}
              >
                <Bell size={20} />
                {notifications.some((n) => !n.read) && (
                  <span className="block absolute top-0 right-0 w-2 h-2 rounded-full bg-accent-4"></span>
                )}
              </button>

              {/* Notifications dropdown */}
              {notificationsOpen && (
                <div className="absolute right-0 z-10 mt-2 w-80 shadow-lg profile-card">
                  <div className="px-4 py-2 border-b border-border">
                    <h3 className="text-sm font-medium text-white">
                      Notifications
                    </h3>
                  </div>
                  <div className="overflow-y-auto max-h-60">
                    {notifications.length > 0 ? (
                      <div className="py-2">
                        {notifications.map((notification) => (
                          <div
                            key={notification.id}
                            className={`px-4 py-3 hover:bg-surface-3 ${
                              !notification.read
                                ? "bg-surface-3 bg-opacity-50"
                                : ""
                            }`}
                          >
                            <p className="text-sm text-white">
                              {notification.message}
                            </p>
                            <p className="mt-1 text-xs text-text-secondary">
                              {notification.time}
                            </p>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="px-4 py-3 text-sm text-text-secondary">
                        No new notifications
                      </div>
                    )}
                  </div>
                  <div className="px-4 py-2 text-center border-t border-border">
                    <button className="text-xs text-soft-blue hover:text-white">
                      Mark all as read
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* User menu */}
            <div className="relative" ref={userMenuRef}>
              <button
                type="button"
                className="flex items-center space-x-2 text-text-secondary hover:text-white focus:outline-none"
                onClick={() => setUserMenuOpen(!userMenuOpen)}
              >
                <div className="flex justify-center items-center w-8 h-8 rounded-full bg-surface-3 text-white text-xs font-medium">
                  {user?.name ? getUserInitials() : <User size={16} />}
                </div>
                <span className="hidden text-sm font-medium md:block">
                  {user?.name || 'User'}
                </span>
                <ChevronDown
                  size={16}
                  className={`transform transition-transform ${
                    userMenuOpen ? "rotate-180" : ""
                  }`}
                />
              </button>

              {/* User dropdown */}
              {userMenuOpen && (
                <div className="absolute right-2 top-10 z-10 w-48 shadow-lg profile-card">
                  {/* User info header */}
                  <div className="px-4 py-3 border-b border-border">
                    <p className="text-sm font-medium text-white">{user?.name || 'User'}</p>
                    <p className="text-xs text-text-secondary">{user?.email || ''}</p>
                    {user?.position && (
                      <p className="text-xs text-text-secondary">{user.position}</p>
                    )}
                  </div>
                  <div className="py-1">
                    <Link
                      to="settings/profile"
                      className="flex items-center px-4 py-2 text-sm text-white hover:bg-surface-3"
                    >
                      <User size={16} className="mr-2" />
                      Profile
                    </Link>
                    <Link
                      to="settings"
                      className="flex items-center px-4 py-2 text-sm text-white hover:bg-surface-3"
                    >
                      <Settings size={16} className="mr-2" />
                      Settings
                    </Link>
                    <div className="my-1 border-t border-border"></div>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full px-4 py-2 text-sm text-white hover:bg-surface-3 text-left"
                    >
                      <LogOut size={16} className="mr-2" />
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default TopNav;
