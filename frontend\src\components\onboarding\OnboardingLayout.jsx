import { useState, useEffect } from 'react';
import { CheckCircle } from 'lucide-react';

const OnboardingLayout = ({ children, currentStep }) => {
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);

  const steps = [
    { id: 1, name: 'Email Verification' },
    { id: 2, name: 'Company Setup' },
    { id: 3, name: 'Invite Team Members' },
  ];

  return (
    <div className="min-h-screen flex flex-col bg-background relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute w-72 h-72 rounded-full bg-soft-purple opacity-5 blur-3xl -top-20 -left-20"></div>
      <div className="absolute w-96 h-96 rounded-full bg-soft-teal opacity-5 blur-3xl -bottom-20 -right-20"></div>
      <div className="absolute w-64 h-64 rounded-full bg-soft-blue opacity-5 blur-3xl top-1/2 left-1/4"></div>
      
      {/* Header with logo */}
      <header className="py-6 px-4 sm:px-6 lg:px-8 flex justify-center">
        <div className="flex items-center">
          <img 
            src="/logo3.png" 
            alt="TeamCheck Logo" 
            className="h-10 w-auto mr-3" 
          />
          <h1 className="text-2xl font-bold text-white">TeamCheck</h1>
        </div>
      </header>
      
      {/* Main content */}
      <main className="flex-1 flex flex-col items-center justify-center px-4 sm:px-6 lg:px-8 py-8">
        <div className="w-full max-w-3xl">
          {/* Steps indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-center">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  {/* Step circle */}
                  <div 
                    className={`flex items-center justify-center h-10 w-10 rounded-full border-2 ${
                      step.id < currentStep 
                        ? 'bg-accent-2 border-accent-2 text-black' 
                        : step.id === currentStep 
                          ? 'border-accent-1 text-white' 
                          : 'border-border text-text-secondary'
                    }`}
                  >
                    {step.id < currentStep ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <span>{step.id}</span>
                    )}
                  </div>
                  
                  {/* Step name */}
                  <div className={`ml-3 ${index < steps.length - 1 ? 'hidden sm:block' : ''}`}>
                    <p 
                      className={`text-sm font-medium ${
                        step.id <= currentStep ? 'text-white' : 'text-text-secondary'
                      }`}
                    >
                      {step.name}
                    </p>
                  </div>
                  
                  {/* Connector line */}
                  {index < steps.length - 1 && (
                    <div className="hidden sm:block w-24 mx-4 h-0.5 bg-border">
                      <div 
                        className={`h-full ${
                          step.id < currentStep ? 'bg-accent-2' : 'bg-transparent'
                        }`}
                        style={{ 
                          width: step.id < currentStep ? '100%' : 
                                 step.id === currentStep ? '50%' : '0%' 
                        }}
                      ></div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          {/* Content with animation */}
          <div 
            className={`glass-card p-8 rounded-lg transition-opacity duration-500 ${
              mounted ? 'opacity-100' : 'opacity-0'
            }`}
          >
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

export default OnboardingLayout;
