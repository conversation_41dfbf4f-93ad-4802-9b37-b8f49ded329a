import { useState, useEffect } from "react";
import {
  Star,
  Calendar,
  Users,
  CheckSquare,
  AlignLeft,
  Target,
  Award,
  Edit,
  Save,
  Trash2,
  X,
} from "lucide-react";
import Modal from "../common/Modal";

const EvaluationDetailsModal = ({ 
  isOpen, 
  onClose, 
  evaluation, 
  onUpdateEvaluation,
  onDeleteEvaluation 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedEvaluation, setEditedEvaluation] = useState(evaluation || {});
  const [activeTab, setActiveTab] = useState("details");

  // Update edited evaluation when the prop changes
  useEffect(() => {
    if (evaluation && evaluation.id !== editedEvaluation?.id) {
      setEditedEvaluation(evaluation);
    }
  }, [evaluation]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedEvaluation({
      ...editedEvaluation,
      [name]: value,
    });
  };

  // Handle star rating click
  const handleStarClick = (rating) => {
    setEditedEvaluation({
      ...editedEvaluation,
      rating,
    });
  };

  // Render star rating input
  const renderStarRatingInput = (editable = false) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={editable ? () => handleStarClick(star) : undefined}
            className={`focus:outline-none ${!editable ? 'cursor-default' : ''}`}
            disabled={!editable}
          >
            <Star
              className={`w-6 h-6 ${
                star <= (editedEvaluation?.rating || 0)
                  ? "text-yellow-400 fill-yellow-400"
                  : "text-gray-400"
              }`}
            />
          </button>
        ))}
        <span className="ml-2 text-white">{(editedEvaluation?.rating || 0).toFixed(1)}</span>
      </div>
    );
  };

  // Handle save
  const handleSave = () => {
    onUpdateEvaluation(editedEvaluation);
    setIsEditing(false);
  };

  // Handle delete
  const handleDelete = () => {
    onDeleteEvaluation(editedEvaluation.id);
    onClose();
  };

  // Get evaluation type icon
  const getEvaluationTypeIcon = (type) => {
    switch (type) {
      case "task-based":
        return <CheckSquare className="h-5 w-5 text-text-secondary" />;
      case "periodic":
        return <Calendar className="h-5 w-5 text-text-secondary" />;
      case "360-degree":
        return <Users className="h-5 w-5 text-text-secondary" />;
      case "competency":
        return <Award className="h-5 w-5 text-text-secondary" />;
      case "goal-based":
        return <Target className="h-5 w-5 text-text-secondary" />;
      default:
        return <CheckSquare className="h-5 w-5 text-text-secondary" />;
    }
  };

  // Get evaluation type name
  const getEvaluationTypeName = (type) => {
    switch (type) {
      case "task-based":
        return "Task-Based Evaluation";
      case "periodic":
        return "Periodic Review";
      case "360-degree":
        return "360° Feedback";
      case "competency":
        return "Competency Assessment";
      case "goal-based":
        return "Goal-Based Evaluation";
      default:
        return "Evaluation";
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (!evaluation) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <div className="flex items-center">
          {getEvaluationTypeIcon(evaluation.type)}
          <span className="ml-2">
            {getEvaluationTypeName(evaluation.type)}
          </span>
        </div>
      }
      size="lg"
    >
      <div className="flex flex-col h-full">
        {/* Tabs */}
        <div className="flex border-b border-border mb-4">
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 ${
              activeTab === "details"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("details")}
          >
            Details
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 ${
              activeTab === "history"
                ? "border-accent-1 text-white"
                : "border-transparent text-text-secondary hover:text-white"
            }`}
            onClick={() => setActiveTab("history")}
          >
            History
          </button>
        </div>

        {activeTab === "details" ? (
          <div className="space-y-4">
            {/* Employee and Project */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  Employee
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="employeeName"
                    value={editedEvaluation.employeeName || ""}
                    onChange={handleInputChange}
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                  />
                ) : (
                  <div className="text-white">{evaluation.employeeName}</div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  Project/Activity
                </label>
                {isEditing ? (
                  <input
                    type="text"
                    name="activityTitle"
                    value={editedEvaluation.activityTitle || ""}
                    onChange={handleInputChange}
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                  />
                ) : (
                  <div className="text-white">{evaluation.activityTitle}</div>
                )}
              </div>
            </div>

            {/* Date and Rating */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  Evaluation Date
                </label>
                {isEditing ? (
                  <input
                    type="date"
                    name="date"
                    value={editedEvaluation.date || ""}
                    onChange={handleInputChange}
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                  />
                ) : (
                  <div className="text-white">{formatDate(evaluation.date)}</div>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-1">
                  Rating
                </label>
                {renderStarRatingInput(isEditing)}
              </div>
            </div>

            {/* Feedback */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-1">
                Feedback
              </label>
              {isEditing ? (
                <textarea
                  name="feedback"
                  value={editedEvaluation.feedback || ""}
                  onChange={handleInputChange}
                  rows="4"
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                ></textarea>
              ) : (
                <div className="bg-surface-3 p-3 rounded-lg text-white text-sm">
                  {evaluation.feedback || "No feedback provided."}
                </div>
              )}
            </div>

            {/* Action buttons */}
            <div className="flex justify-end space-x-3 mt-6">
              {isEditing ? (
                <>
                  <button
                    type="button"
                    onClick={() => setIsEditing(false)}
                    className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleSave}
                    className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </button>
                </>
              ) : (
                <>
                  <button
                    type="button"
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-500 bg-opacity-20 text-red-400 hover:bg-opacity-30 font-medium rounded-lg text-sm flex items-center"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                  <button
                    type="button"
                    onClick={() => setIsEditing(true)}
                    className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </button>
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="text-center text-text-secondary py-8">
              Evaluation history will be displayed here.
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default EvaluationDetailsModal;
