import { useState, useEffect } from "react";
import {
  Star,
  Calendar,
  Users,
  CheckSquare,
  AlignLeft,
  Briefcase,
} from "lucide-react";
import Modal from "../common/Modal";

const SimplifiedEvaluationModal = ({ isOpen, onClose, onCreateEvaluation, evaluationType = "periodic", projectId = null }) => {
  // Mock data for employees
  const mockEmployees = [
    { id: 1, name: "<PERSON>", position: "Frontend Developer", dateJoined: "2023-01-15" },
    { id: 2, name: "<PERSON>", position: "UX Designer", dateJoined: "2022-08-10" },
    { id: 3, name: "<PERSON>", position: "Backend Developer", dateJoined: "2023-03-22" },
    { id: 4, name: "<PERSON>", position: "Project Manager", dateJoined: "2021-11-05" },
  ];

  // Mock data for projects
  const mockProjects = [
    { id: 1, title: "Website Redesign" },
    { id: 2, title: "Mobile App Development" },
    { id: 3, title: "Client Onboarding Improvement" },
    { id: 4, title: "Database Migration" },
  ];

  // State for form data
  const [formData, setFormData] = useState({
    type: evaluationType,
    employeeId: "",
    projectId: projectId || "",
    rating: 0,
    feedback: "",
    strengths: "",
    improvements: "",
    date: new Date().toISOString().split("T")[0],
  });

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        type: evaluationType,
        employeeId: "",
        projectId: projectId || "",
        rating: 0,
        feedback: "",
        strengths: "",
        improvements: "",
        date: new Date().toISOString().split("T")[0],
      });
    }
  }, [isOpen, evaluationType, projectId]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle star rating click (1-10 scale)
  const handleStarClick = (rating) => {
    setFormData({
      ...formData,
      rating,
    });
  };

  // Render star rating input (1-10 scale)
  const renderStarRatingInput = () => {
    return (
      <div className="flex flex-wrap items-center gap-1">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => handleStarClick(star)}
            className={`w-8 h-8 rounded-full flex items-center justify-center focus:outline-none ${
              star <= formData.rating
                ? "bg-soft-blue text-white"
                : "bg-surface-3 text-text-secondary"
            }`}
          >
            {star}
          </button>
        ))}
        {formData.rating > 0 && (
          <span className="ml-2 text-white">{formData.rating}/10</span>
        )}
      </div>
    );
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Create evaluation object
    const evaluation = {
      id: Date.now(),
      type: formData.type,
      employeeId: formData.employeeId,
      projectId: formData.projectId,
      rating: formData.rating,
      feedback: formData.feedback,
      strengths: formData.strengths,
      improvements: formData.improvements,
      date: formData.date,
    };
    
    onCreateEvaluation(evaluation);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={formData.type === "periodic" ? "Create Periodic Evaluation" : "Create Project Review"}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Employee Selection */}
        <div>
          <label htmlFor="employeeId" className="block text-sm font-medium text-white mb-1">
            Employee
          </label>
          <select
            id="employeeId"
            name="employeeId"
            value={formData.employeeId}
            onChange={handleInputChange}
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
            required
          >
            <option value="">Select Employee</option>
            {mockEmployees.map((employee) => (
              <option key={employee.id} value={employee.id}>
                {employee.name} - {employee.position}
              </option>
            ))}
          </select>
        </div>

        {/* Project Selection (only for project reviews) */}
        {formData.type === "project" && (
          <div>
            <label htmlFor="projectId" className="block text-sm font-medium text-white mb-1">
              Project
            </label>
            <select
              id="projectId"
              name="projectId"
              value={formData.projectId}
              onChange={handleInputChange}
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
              required
            >
              <option value="">Select Project</option>
              {mockProjects.map((project) => (
                <option key={project.id} value={project.id}>
                  {project.title}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Evaluation Date */}
        <div>
          <label htmlFor="date" className="block text-sm font-medium text-white mb-1">
            Evaluation Date
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Calendar className="h-5 w-5 text-text-secondary" />
            </div>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleInputChange}
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              required
            />
          </div>
        </div>

        {/* Performance Rating */}
        <div>
          <label className="block text-sm font-medium text-white mb-1">
            Performance Rating (1-10)
          </label>
          {renderStarRatingInput()}
        </div>

        {/* Strengths */}
        <div>
          <label htmlFor="strengths" className="block text-sm font-medium text-white mb-1">
            Strengths
          </label>
          <textarea
            id="strengths"
            name="strengths"
            value={formData.strengths}
            onChange={handleInputChange}
            rows="2"
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
            placeholder="List key strengths demonstrated..."
          ></textarea>
        </div>

        {/* Areas for Improvement */}
        <div>
          <label htmlFor="improvements" className="block text-sm font-medium text-white mb-1">
            Areas for Improvement
          </label>
          <textarea
            id="improvements"
            name="improvements"
            value={formData.improvements}
            onChange={handleInputChange}
            rows="2"
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
            placeholder="List areas that need improvement..."
          ></textarea>
        </div>

        {/* Overall Feedback */}
        <div>
          <label htmlFor="feedback" className="block text-sm font-medium text-white mb-1">
            Overall Feedback
          </label>
          <textarea
            id="feedback"
            name="feedback"
            value={formData.feedback}
            onChange={handleInputChange}
            rows="3"
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
            placeholder="Provide detailed feedback on performance..."
            required
          ></textarea>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
          >
            Create Evaluation
          </button>
        </div>
      </form>
    </Modal>
  );
};

export default SimplifiedEvaluationModal;
