import { useState } from "react";
import {
  Briefcase,
  Users,
  Calendar,
  AlignLeft,
  Check,
  ChevronDown,
  X,
} from "lucide-react";
import SlideInModal from "../common/SlideInModal";

const CreateProjectModal = ({ isOpen, onClose, onCreateProject }) => {
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    dueDate: "",
    teamMembers: [], // Array of { id, role }
    projectFormat: "statuses", // 'statuses' or 'milestones'
  });

  const [showTeamDropdown, setShowTeamDropdown] = useState(false);
  const [selectedRole, setSelectedRole] = useState("member"); // 'manager' or 'member'
  const [selectedMembers, setSelectedMembers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Mock team members for selection
  const teamMembers = [
    { id: 1, name: "<PERSON>", position: "<PERSON><PERSON><PERSON>" },
    { id: 2, name: "<PERSON>", position: "Designer" },
    { id: 3, name: "<PERSON>", position: "Project Manager" },
    { id: 4, name: "<PERSON>", position: "Marketing" },
    { id: 5, name: "<PERSON> Chen", position: "Developer" },
    { id: 6, name: "Lisa Brown", position: "UX Designer" },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Toggle select all members
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedMembers([]);
    } else {
      setSelectedMembers(teamMembers.map((member) => member.id));
    }
    setSelectAll(!selectAll);
  };

  // Toggle individual member selection
  const handleMemberToggle = (memberId) => {
    setSelectedMembers((prev) => {
      const isSelected = prev.includes(memberId);
      return isSelected
        ? prev.filter((id) => id !== memberId)
        : [...prev, memberId];
    });

    // Update selectAll state based on selection
    if (selectedMembers.length + 1 === teamMembers.length && !selectAll) {
      setSelectAll(true);
    } else if (selectAll) {
      setSelectAll(false);
    }
  };

  // Add selected members with the current role
  const handleAddMembers = () => {
    const newTeamMembers = [
      ...formData.teamMembers.filter(
        (member) => !selectedMembers.includes(member.id)
      ),
      ...selectedMembers.map((id) => ({
        id,
        role: selectedRole,
      })),
    ];

    setFormData({
      ...formData,
      teamMembers: newTeamMembers,
    });

    // Reset selection
    setSelectedMembers([]);
    setSelectAll(false);
    setShowTeamDropdown(false);
  };

  // Remove a member from the team
  const handleRemoveMember = (memberId) => {
    setFormData({
      ...formData,
      teamMembers: formData.teamMembers.filter(
        (member) => member.id !== memberId
      ),
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    // Create a new project with default columns for Kanban
    const newProject = {
      ...formData,
      id: Date.now(),
      createdAt: new Date().toISOString(),
      tasks: [],
      columns: [
        { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
        { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
        { id: "completed", title: "Completed", color: "bg-green-500" },
      ],
      // If project format is milestones, add empty milestones array
      ...(formData.projectFormat === "milestones" && {
        milestones: [],
        milestoneProgress: 0,
      }),
    };

    onCreateProject(newProject);
    onClose();

    // Reset form
    setFormData({
      title: "",
      description: "",
      dueDate: "",
      teamMembers: [],
      projectFormat: "statuses",
    });

    // Reset other state
    setSelectedMembers([]);
    setSelectAll(false);
    setSelectedRole("member");
  };

  return (
    <SlideInModal
      isOpen={isOpen}
      onClose={onClose}
      title="Create New Project"
      width="600px"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4">
          {/* Project Title */}
          <div>
            <label
              htmlFor="title"
              className="block text-sm font-medium text-white mb-1"
            >
              Project Title
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Briefcase className="h-5 w-5 text-text-secondary" />
              </div>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Enter project title"
                required
              />
            </div>
          </div>

          {/* Project Description */}
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-white mb-1"
            >
              Description
            </label>
            <div className="relative">
              <div className="absolute top-3 left-3 flex items-start pointer-events-none">
                <AlignLeft className="h-5 w-5 text-text-secondary" />
              </div>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="3"
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
                placeholder="Enter project description"
              ></textarea>
            </div>
          </div>

          {/* Due Date */}
          <div>
            <label
              htmlFor="dueDate"
              className="block text-sm font-medium text-white mb-1"
            >
              Due Date (Optional)
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Calendar className="h-5 w-5 text-text-secondary" />
              </div>
              <input
                type="date"
                id="dueDate"
                name="dueDate"
                value={formData.dueDate}
                onChange={handleInputChange}
                className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
              />
            </div>
          </div>

          {/* Project Format Selection */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Project Format
            </label>
            <div className="grid grid-cols-2 gap-3">
              <button
                type="button"
                className={`p-3 flex flex-col items-center justify-center rounded-lg border ${
                  formData.projectFormat === "statuses"
                    ? "border-accent-1 bg-surface-2"
                    : "border-border bg-surface-3"
                }`}
                onClick={() =>
                  setFormData({ ...formData, projectFormat: "statuses" })
                }
              >
                <div className="w-full flex justify-between mb-2">
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
                <span className="text-xs font-medium text-white">
                  Statuses Only
                </span>
                <p className="text-xs text-text-secondary mt-1">
                  Simple kanban or list view
                </p>
              </button>

              <button
                type="button"
                className={`p-3 flex flex-col items-center justify-center rounded-lg border ${
                  formData.projectFormat === "milestones"
                    ? "border-accent-1 bg-surface-2"
                    : "border-border bg-surface-3"
                }`}
                onClick={() =>
                  setFormData({ ...formData, projectFormat: "milestones" })
                }
              >
                <div className="w-full flex items-center justify-between mb-2">
                  <div className="w-2 h-2 rounded-full bg-white/50"></div>
                  <div className="w-full h-0.5 bg-white/20"></div>
                  <div className="w-2 h-2 rounded-full bg-white/50"></div>
                  <div className="w-full h-0.5 bg-white/20"></div>
                  <div className="w-2 h-2 rounded-full bg-white/50"></div>
                </div>
                <span className="text-xs font-medium text-white">
                  With Milestones
                </span>
                <p className="text-xs text-text-secondary mt-1">
                  Track progress with milestones
                </p>
              </button>
            </div>
          </div>

          {/* Team Members */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Team Members
            </label>

            {/* Selected team members display */}
            {formData.teamMembers.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {formData.teamMembers.map((teamMember) => {
                  const member = teamMembers.find(
                    (m) => m.id === teamMember.id
                  );
                  if (!member) return null;

                  return (
                    <div
                      key={teamMember.id}
                      className={`px-2 py-1 rounded-lg text-xs flex items-center ${
                        teamMember.role === "manager"
                          ? "bg-soft-blue bg-opacity-20 text-soft-blue"
                          : "bg-soft-purple bg-opacity-20 text-soft-purple"
                      }`}
                    >
                      <span>{member.name}</span>
                      <span className="mx-1 text-text-secondary">•</span>
                      <span>{teamMember.role}</span>
                      <button
                        type="button"
                        className="ml-1 text-text-secondary hover:text-white"
                        onClick={() => handleRemoveMember(teamMember.id)}
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Team member selection */}
            <div className="relative">
              <button
                type="button"
                className="w-full flex justify-between items-center px-3 py-2 text-sm text-white bg-surface-3 border border-border rounded-lg"
                onClick={() => setShowTeamDropdown(!showTeamDropdown)}
              >
                <div className="flex items-center">
                  <Users className="mr-2 w-4 h-4 text-text-secondary" />
                  <span>Add team members</span>
                </div>
                <ChevronDown className="w-4 h-4 text-text-secondary" />
              </button>

              {showTeamDropdown && (
                <div className="absolute z-10 mt-1 w-full bg-surface-2 border border-border rounded-lg shadow-lg">
                  <div className="p-2 border-b border-border">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="select-all"
                          checked={selectAll}
                          onChange={handleSelectAll}
                          className="w-4 h-4 text-accent-1 bg-surface border-border rounded focus:ring-accent-1"
                        />
                        <label
                          htmlFor="select-all"
                          className="ml-2 text-sm text-white"
                        >
                          Select All
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-text-secondary">
                          Role:
                        </span>
                        <div className="flex rounded-lg overflow-hidden border border-border">
                          <button
                            type="button"
                            className={`px-2 py-1 text-xs ${
                              selectedRole === "manager"
                                ? "bg-surface text-white"
                                : "bg-surface-3 text-text-secondary"
                            }`}
                            onClick={() => setSelectedRole("manager")}
                          >
                            Manager
                          </button>
                          <button
                            type="button"
                            className={`px-2 py-1 text-xs ${
                              selectedRole === "member"
                                ? "bg-surface text-white"
                                : "bg-surface-3 text-text-secondary"
                            }`}
                            onClick={() => setSelectedRole("member")}
                          >
                            Member
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="max-h-40 overflow-y-auto p-2">
                    {teamMembers.map((member) => (
                      <div
                        key={member.id}
                        className="flex items-center mb-2 last:mb-0"
                      >
                        <input
                          type="checkbox"
                          id={`select-member-${member.id}`}
                          checked={selectedMembers.includes(member.id)}
                          onChange={() => handleMemberToggle(member.id)}
                          className="w-4 h-4 text-accent-1 bg-surface border-border rounded focus:ring-accent-1"
                        />
                        <label
                          htmlFor={`select-member-${member.id}`}
                          className="ml-2 text-sm text-white flex items-center justify-between w-full"
                        >
                          <span>{member.name}</span>
                          <span className="text-xs text-text-secondary">
                            {member.position}
                          </span>
                        </label>
                      </div>
                    ))}
                  </div>

                  <div className="p-2 border-t border-border flex justify-end">
                    <button
                      type="button"
                      className="px-3 py-1 text-xs text-white bg-surface-3 hover:bg-surface rounded-lg"
                      onClick={handleAddMembers}
                    >
                      Add Selected
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
          >
            Create Project
          </button>
        </div>
      </form>
    </SlideInModal>
  );
};

export default CreateProjectModal;
