import { useState, useEffect } from "react";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { Plus, MoreVertical } from "lucide-react";
import TaskCard from "./TaskCard";

const KanbanBoard = ({ project, onTaskClick, onAddTask, onTaskMove }) => {
  const [showColumnMenu, setShowColumnMenu] = useState(null);

  // Force re-render on strict mode
  const [enabled, setEnabled] = useState(false);

  useEffect(() => {
    // This is to fix the issue with react-beautiful-dnd in React 18+ strict mode
    const timeout = setTimeout(() => {
      setEnabled(true);
    }, 500);

    return () => clearTimeout(timeout);
  }, []);

  // Default columns if not provided
  const defaultColumns = [
    { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
    { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
    { id: "completed", title: "Completed", color: "bg-green-500" },
  ];

  // Use project columns or default columns
  const columns = project.columns || defaultColumns;

  // Handle drag end event
  const handleDragEnd = (result) => {
    const { source, destination, draggableId } = result;

    // Dropped outside a droppable area
    if (!destination) return;

    // Dropped in the same position
    if (
      source.droppableId === destination.droppableId &&
      source.index === destination.index
    )
      return;

    // Call the parent handler with the move details
    onTaskMove({
      taskId: draggableId,
      sourceColumnId: source.droppableId,
      destinationColumnId: destination.droppableId,
      sourceIndex: source.index,
      destinationIndex: destination.index,
    });
  };

  // Toggle column menu
  const toggleColumnMenu = (columnId, e) => {
    e.stopPropagation();
    setShowColumnMenu(showColumnMenu === columnId ? null : columnId);
  };

  // Close all menus when clicking outside
  const handleOutsideClick = () => {
    setShowColumnMenu(null);
  };

  // Get tasks for a specific column
  const getColumnTasks = (columnId) => {
    return project.tasks.filter(
      (task) => task.status.toLowerCase().replace(/\s+/g, "-") === columnId
    );
  };

  return (
    <div className="h-full" onClick={handleOutsideClick}>
      {enabled ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <div className="flex space-x-4 h-full overflow-x-auto pb-4">
            {columns.map((column) => (
              <div
                key={column.id}
                className="flex-shrink-0 w-72 flex flex-col bg-surface-3 rounded-lg border border-border"
              >
                {/* Column header */}
                <div className="p-3 border-b border-border flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className={`w-3 h-3 rounded-full ${column.color} mr-2`}
                    ></div>
                    <h3 className="text-sm font-medium text-white">
                      {column.title}
                    </h3>
                    <span className="ml-2 text-xs text-text-secondary">
                      {getColumnTasks(column.id).length}
                    </span>
                  </div>
                  <div className="relative">
                    <button
                      className="p-1 rounded-full hover:bg-surface text-text-secondary"
                      onClick={(e) => toggleColumnMenu(column.id, e)}
                    >
                      <MoreVertical className="h-4 w-4" />
                    </button>

                    {showColumnMenu === column.id && (
                      <div className="absolute right-0 mt-1 w-40 bg-surface border border-border rounded-md shadow-lg z-10">
                        <ul className="py-1 text-sm">
                          <li className="px-3 py-2 hover:bg-surface-2 cursor-pointer text-white">
                            Edit Column
                          </li>
                          <li className="px-3 py-2 hover:bg-surface-2 cursor-pointer text-white">
                            Sort Tasks
                          </li>
                          <li className="px-3 py-2 hover:bg-surface-2 cursor-pointer text-red-400">
                            Clear Column
                          </li>
                        </ul>
                      </div>
                    )}
                  </div>
                </div>

                {/* Column content - droppable area */}
                <Droppable droppableId={column.id}>
                  {(provided, snapshot) => (
                    <div
                      ref={provided.innerRef}
                      {...provided.droppableProps}
                      className={`flex-1 p-2 overflow-y-auto ${
                        snapshot.isDraggingOver ? "bg-surface-2" : ""
                      }`}
                      style={{ minHeight: "200px" }}
                    >
                      {getColumnTasks(column.id).map((task, index) => (
                        <Draggable
                          key={task.id}
                          draggableId={task.id.toString()}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                ...provided.draggableProps.style,
                                opacity: snapshot.isDragging ? "0.8" : "1",
                              }}
                            >
                              <TaskCard
                                task={task}
                                onClick={() => onTaskClick(task)}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>

                {/* Add task button */}
                <div className="p-2 border-t border-border">
                  <button
                    className="w-full p-2 text-sm text-text-secondary hover:text-white bg-surface hover:bg-surface-2 rounded flex items-center justify-center"
                    onClick={() => onAddTask(column.id)}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Task
                  </button>
                </div>
              </div>
            ))}

            {/* Add new column button */}
            <div className="flex-shrink-0 w-72 bg-surface-3 rounded-lg border border-border border-dashed flex items-center justify-center">
              <button className="p-4 text-text-secondary hover:text-white flex items-center">
                <Plus className="h-5 w-5 mr-2" />
                Add Column
              </button>
            </div>
          </div>
        </DragDropContext>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-text-secondary">Loading Kanban board...</p>
        </div>
      )}
    </div>
  );
};

export default KanbanBoard;
