import { useState } from 'react';
import { Plus, X, Edit2, Check, ChevronRight, Calendar } from 'lucide-react';

const MilestonesView = ({ project, onUpdateProject }) => {
  const [showAddMilestone, setShowAddMilestone] = useState(false);
  const [editingMilestoneId, setEditingMilestoneId] = useState(null);
  const [newMilestone, setNewMilestone] = useState({
    title: '',
    description: '',
    dueDate: '',
    progress: 0
  });

  // Handle adding a new milestone
  const handleAddMilestone = () => {
    if (!newMilestone.title.trim()) return;
    
    const updatedProject = {
      ...project,
      milestones: [
        ...(project.milestones || []),
        {
          id: Date.now().toString(),
          ...newMilestone,
          createdAt: new Date().toISOString()
        }
      ]
    };
    
    onUpdateProject(updatedProject);
    setNewMilestone({ title: '', description: '', dueDate: '', progress: 0 });
    setShowAddMilestone(false);
  };

  // Handle updating a milestone
  const handleUpdateMilestone = (milestoneId, updatedData) => {
    const updatedMilestones = project.milestones.map(milestone => 
      milestone.id === milestoneId ? { ...milestone, ...updatedData } : milestone
    );
    
    // Calculate overall project milestone progress
    const totalMilestones = updatedMilestones.length;
    const totalProgress = updatedMilestones.reduce((sum, milestone) => sum + milestone.progress, 0);
    const overallProgress = totalMilestones > 0 ? Math.round(totalProgress / totalMilestones) : 0;
    
    const updatedProject = {
      ...project,
      milestones: updatedMilestones,
      milestoneProgress: overallProgress
    };
    
    onUpdateProject(updatedProject);
    setEditingMilestoneId(null);
  };

  // Handle deleting a milestone
  const handleDeleteMilestone = (milestoneId) => {
    const updatedMilestones = project.milestones.filter(milestone => milestone.id !== milestoneId);
    
    // Calculate overall project milestone progress
    const totalMilestones = updatedMilestones.length;
    const totalProgress = updatedMilestones.reduce((sum, milestone) => sum + milestone.progress, 0);
    const overallProgress = totalMilestones > 0 ? Math.round(totalProgress / totalMilestones) : 0;
    
    const updatedProject = {
      ...project,
      milestones: updatedMilestones,
      milestoneProgress: overallProgress
    };
    
    onUpdateProject(updatedProject);
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'No date set';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
  };

  return (
    <div className="space-y-4">
      {/* Project milestone progress */}
      <div className="p-4 bg-surface-3 rounded-lg border border-border">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-sm font-medium text-white">Overall Progress</h3>
          <span className="text-sm text-white">{project.milestoneProgress || 0}%</span>
        </div>
        <div className="w-full bg-surface rounded-full h-2">
          <div
            className="bg-soft-blue h-2 rounded-full"
            style={{ width: `${project.milestoneProgress || 0}%` }}
          ></div>
        </div>
      </div>
      
      {/* Milestones list */}
      <div className="space-y-3">
        {project.milestones && project.milestones.map((milestone) => (
          <div 
            key={milestone.id} 
            className="p-4 bg-surface-3 rounded-lg border border-border"
          >
            {editingMilestoneId === milestone.id ? (
              // Editing mode
              <div className="space-y-3">
                <input
                  type="text"
                  value={milestone.title}
                  onChange={(e) => handleUpdateMilestone(milestone.id, { title: e.target.value })}
                  className="w-full p-2 bg-surface-2 border border-border rounded-lg text-white"
                  placeholder="Milestone title"
                />
                
                <textarea
                  value={milestone.description}
                  onChange={(e) => handleUpdateMilestone(milestone.id, { description: e.target.value })}
                  className="w-full p-2 bg-surface-2 border border-border rounded-lg text-white"
                  placeholder="Description"
                  rows="2"
                ></textarea>
                
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <label className="block text-xs text-text-secondary mb-1">Due Date</label>
                    <input
                      type="date"
                      value={milestone.dueDate}
                      onChange={(e) => handleUpdateMilestone(milestone.id, { dueDate: e.target.value })}
                      className="w-full p-2 bg-surface-2 border border-border rounded-lg text-white"
                    />
                  </div>
                  
                  <div className="flex-1">
                    <label className="block text-xs text-text-secondary mb-1">Progress</label>
                    <input
                      type="range"
                      min="0"
                      max="100"
                      step="5"
                      value={milestone.progress}
                      onChange={(e) => handleUpdateMilestone(milestone.id, { progress: parseInt(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-text-secondary">
                      <span>0%</span>
                      <span>{milestone.progress}%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="button"
                    className="p-2 text-white bg-surface-2 rounded-lg hover:bg-surface"
                    onClick={() => setEditingMilestoneId(null)}
                  >
                    <Check className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ) : (
              // View mode
              <div>
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-sm font-medium text-white">{milestone.title}</h3>
                    {milestone.description && (
                      <p className="text-xs text-text-secondary mt-1">{milestone.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      className="p-1 text-text-secondary hover:text-white"
                      onClick={() => setEditingMilestoneId(milestone.id)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </button>
                    <button
                      type="button"
                      className="p-1 text-text-secondary hover:text-white"
                      onClick={() => handleDeleteMilestone(milestone.id)}
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center text-xs text-text-secondary">
                    <Calendar className="w-3 h-3 mr-1" />
                    <span>{formatDate(milestone.dueDate)}</span>
                  </div>
                  <div className="text-xs text-white">{milestone.progress}%</div>
                </div>
                
                <div className="w-full bg-surface rounded-full h-1.5 mt-2">
                  <div
                    className="bg-soft-blue h-1.5 rounded-full"
                    style={{ width: `${milestone.progress}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        ))}
        
        {/* Add milestone form */}
        {showAddMilestone ? (
          <div className="p-4 bg-surface-3 rounded-lg border border-border">
            <div className="space-y-3">
              <input
                type="text"
                value={newMilestone.title}
                onChange={(e) => setNewMilestone({ ...newMilestone, title: e.target.value })}
                className="w-full p-2 bg-surface-2 border border-border rounded-lg text-white"
                placeholder="Milestone title"
              />
              
              <textarea
                value={newMilestone.description}
                onChange={(e) => setNewMilestone({ ...newMilestone, description: e.target.value })}
                className="w-full p-2 bg-surface-2 border border-border rounded-lg text-white"
                placeholder="Description"
                rows="2"
              ></textarea>
              
              <div className="flex items-center">
                <div className="flex-1">
                  <label className="block text-xs text-text-secondary mb-1">Due Date</label>
                  <input
                    type="date"
                    value={newMilestone.dueDate}
                    onChange={(e) => setNewMilestone({ ...newMilestone, dueDate: e.target.value })}
                    className="w-full p-2 bg-surface-2 border border-border rounded-lg text-white"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  className="px-3 py-1 text-sm text-white bg-surface-2 rounded-lg hover:bg-surface"
                  onClick={() => setShowAddMilestone(false)}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  className="px-3 py-1 text-sm text-white bg-soft-blue rounded-lg hover:bg-opacity-80"
                  onClick={handleAddMilestone}
                >
                  Add Milestone
                </button>
              </div>
            </div>
          </div>
        ) : (
          <button
            type="button"
            className="w-full p-3 flex items-center justify-center text-text-secondary hover:text-white bg-surface-3 border border-dashed border-border rounded-lg hover:border-soft-blue"
            onClick={() => setShowAddMilestone(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            <span>Add Milestone</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default MilestonesView;
