import { useState } from "react";
import { X, Star } from "lucide-react";

const ProjectReviewModal = ({ isOpen, onClose, projectId, onCreateReview }) => {
  const [review, setReview] = useState({
    projectId: projectId,
    rating: 0,
    strengths: "",
    improvements: "",
    feedback: "",
    employeeId: "",
    date: new Date().toISOString().split("T")[0],
    type: "project",
    id: Math.floor(Math.random() * 1000) + 100, // Generate a random ID for demo
  });

  // Mock team members for the project
  const teamMembers = [
    { id: 1, name: "<PERSON>", role: "<PERSON><PERSON><PERSON>" },
    { id: 2, name: "<PERSON>", role: "Designer" },
    { id: 3, name: "<PERSON>", role: "Project Manager" },
    { id: 4, name: "<PERSON>", role: "Marketing" },
  ];

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setReview({
      ...review,
      [name]: value,
    });
  };

  const handleRatingChange = (rating) => {
    setReview({
      ...review,
      rating,
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onCreateReview(review);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div
        className="fixed inset-0 bg-black bg-opacity-70"
        onClick={onClose}
      ></div>
      <div className="relative bg-surface-2 rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 z-10 flex justify-between items-center px-6 py-4 border-b border-border bg-surface-2">
          <h2 className="text-xl font-bold text-white">Project Review</h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-surface-3 text-text-secondary hover:text-white"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          <div>
            <label
              htmlFor="employeeId"
              className="block text-sm font-medium text-white mb-2"
            >
              Team Member
            </label>
            <select
              id="employeeId"
              name="employeeId"
              value={review.employeeId}
              onChange={handleInputChange}
              required
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
            >
              <option value="">Select team member</option>
              {teamMembers.map((member) => (
                <option key={member.id} value={member.id}>
                  {member.name} ({member.role})
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-2">
              Performance Rating (1-10)
            </label>
            <div className="flex flex-wrap items-center gap-1">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => handleRatingChange(star)}
                  className={`w-8 h-8 rounded-full flex items-center justify-center focus:outline-none ${
                    star <= review.rating
                      ? "bg-soft-blue text-white"
                      : "bg-surface-3 text-text-secondary"
                  }`}
                >
                  {star}
                </button>
              ))}
              <span className="ml-2 text-white">{review.rating}/10</span>
            </div>
          </div>

          <div>
            <label
              htmlFor="strengths"
              className="block text-sm font-medium text-white mb-2"
            >
              Strengths
            </label>
            <textarea
              id="strengths"
              name="strengths"
              value={review.strengths}
              onChange={handleInputChange}
              rows="3"
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
              placeholder="List key strengths demonstrated in this project..."
            ></textarea>
          </div>

          <div>
            <label
              htmlFor="improvements"
              className="block text-sm font-medium text-white mb-2"
            >
              Areas for Improvement
            </label>
            <textarea
              id="improvements"
              name="improvements"
              value={review.improvements}
              onChange={handleInputChange}
              rows="3"
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
              placeholder="List areas that need improvement..."
            ></textarea>
          </div>

          <div>
            <label
              htmlFor="feedback"
              className="block text-sm font-medium text-white mb-2"
            >
              Overall Feedback
            </label>
            <textarea
              id="feedback"
              name="feedback"
              value={review.feedback}
              onChange={handleInputChange}
              rows="4"
              className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
              placeholder="Provide detailed feedback on performance in this project..."
            ></textarea>
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-surface-3 text-white rounded-lg hover:bg-surface"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-soft-blue text-white rounded-lg hover:bg-soft-blue/80"
            >
              Create Review
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectReviewModal;
