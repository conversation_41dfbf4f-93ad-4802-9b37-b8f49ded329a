import { useState } from "react";
import { Users, Calendar, Plus } from "lucide-react";
import { Link } from "react-router-dom";

const ProjectsTable = ({ projects, onNewProject }) => {
  const [filter, setFilter] = useState("all");

  // Filter projects based on status
  const filteredProjects = projects.filter((project) => {
    if (filter === "all") return true;

    // Calculate project status
    const totalTasks = project.tasks?.length || 0;
    const completedTasks =
      project.tasks?.filter((task) => task.status === "Completed").length || 0;
    const progress =
      totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

    if (filter === "completed" && progress === 100) return true;
    if (filter === "in-progress" && progress > 0 && progress < 100) return true;
    if (filter === "not-started" && progress === 0) return true;

    return false;
  });

  // Get status color
  const getStatusColor = (progress) => {
    if (progress === 100) return "bg-green-500";
    if (progress > 0) return "bg-blue-500";
    return "bg-yellow-500";
  };

  // Get status text
  const getStatusText = (progress) => {
    if (progress === 100) return "Completed";
    if (progress > 0) return "In Progress";
    return "Not Started";
  };

  return (
    <div className="space-y-6">
      {/* Projects header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Projects</h2>
          <p className="mt-1 text-sm text-text-secondary">
            Manage your projects and tasks with Kanban boards
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <button
            className="flex items-center px-4 py-2 text-white rounded-lg bg-surface-3 hover:bg-surface btn-white"
            onClick={onNewProject}
          >
            <Plus className="mr-2 w-4 h-4" />
            New Project
          </button>
        </div>
      </div>

      {/* Projects grid */}
      <div className="flex flex-wrap gap-4">
        {filteredProjects.map((project) => {
          // Calculate overall project completion
          let progress = 0;

          // If project has milestones, use milestone progress
          if (
            project.projectFormat === "milestones" &&
            project.milestoneProgress !== undefined
          ) {
            progress = project.milestoneProgress;
          } else {
            // Otherwise calculate from tasks
            const totalTasks = project.tasks?.length || 0;
            const completedTasks =
              project.tasks?.filter((task) => task.status === "Completed")
                .length || 0;
            progress =
              totalTasks > 0
                ? Math.round((completedTasks / totalTasks) * 100)
                : 0;
          }

          return (
            <div
              key={project.id}
              className="flex-grow p-4 max-w-xs rounded-lg border transition-colors cursor-pointer border-border bg-surface-3 hover:border-soft-blue basis-64"
              onClick={() =>
                (window.location.href = `/dashboard/projects/${project.id}`)
              }
            >
              <div className="flex justify-between items-start mb-2">
                <h4 className="font-medium text-white text-md">
                  {project.title}
                </h4>
                <span
                  className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                    progress
                  )} bg-opacity-10 text-white`}
                >
                  {getStatusText(progress)}
                </span>
              </div>
              <p className="mb-3 text-xs text-text-secondary line-clamp-2">
                {project.description}
              </p>
              <div className="flex justify-between items-center text-xs text-text-secondary">
                <div className="flex items-center">
                  <Users className="mr-1 w-3 h-3" />
                  <span>{project.team?.length || 0}</span>
                </div>
                <div className="flex items-center">
                  <Calendar className="mr-1 w-3 h-3" />
                  <span>{new Date(project.dueDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center">
                  <div className="w-16 bg-surface-3 rounded-full h-1.5 mr-1">
                    <div
                      className="bg-soft-blue h-1.5 rounded-full"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  <span>{progress}%</span>
                  {project.projectFormat === "milestones" && (
                    <span className="ml-2 text-xs px-1.5 py-0.5 rounded bg-soft-purple bg-opacity-10 text-soft-purple">
                      Milestones
                    </span>
                  )}
                </div>
              </div>
            </div>
          );
        })}

        {/* Add project card */}
        <div
          onClick={onNewProject}
          className="flex flex-col flex-grow justify-center items-center p-4 max-w-xs rounded-lg border border-dashed transition-colors cursor-pointer border-border bg-surface-3 hover:border-soft-blue basis-64 text-text-secondary hover:text-white"
        >
          <Plus className="mb-2 w-8 h-8" />
          <span className="text-sm">Add New Project</span>
        </div>
      </div>

      {/* Empty state */}
      {projects.length === 0 && (
        <div className="p-8 text-center rounded-lg glass-card">
          <div className="mx-auto mb-4 w-16 h-16 text-text-secondary">📋</div>
          <h3 className="mb-2 text-xl font-medium text-white">
            No Projects Yet
          </h3>
          <p className="mb-6 text-text-secondary">
            Create your first project to get started with task management
          </p>
          <button
            className="flex items-center px-4 py-2 mx-auto text-white rounded-lg bg-surface-3 hover:bg-surface"
            onClick={onNewProject}
          >
            <Plus className="mr-2 w-4 h-4" />
            Create Your First Project
          </button>
        </div>
      )}
    </div>
  );
};

export default ProjectsTable;
