import { useState } from 'react';
import { CheckSquare, Clock, Users, MoreVertical, CheckCircle } from 'lucide-react';

const TaskCard = ({ task, onClick }) => {
  const [showMenu, setShowMenu] = useState(false);

  // Calculate completion percentage based on checklist items
  const getCompletionPercentage = () => {
    if (!task.checklist || task.checklist.length === 0) return 0;
    const completed = task.checklist.filter(item => item.completed).length;
    return Math.round((completed / task.checklist.length) * 100);
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-500';
      case 'In Progress':
        return 'bg-blue-500';
      case 'Not Started':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Format date to readable format
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const toggleMenu = (e) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };

  return (
    <div 
      className="bg-surface-2 border border-border rounded-lg p-3 mb-3 cursor-pointer hover:border-soft-blue transition-colors shadow-sm"
      onClick={onClick}
    >
      {/* Task header */}
      <div className="flex justify-between items-start mb-2">
        <h4 className="text-sm font-medium text-white">{task.title}</h4>
        <div className="relative">
          <button 
            className="p-1 rounded-full hover:bg-surface-3 text-text-secondary"
            onClick={toggleMenu}
          >
            <MoreVertical className="h-4 w-4" />
          </button>
          
          {showMenu && (
            <div className="absolute right-0 mt-1 w-40 bg-surface-3 border border-border rounded-md shadow-lg z-10">
              <ul className="py-1 text-sm">
                <li className="px-3 py-2 hover:bg-surface cursor-pointer text-white">Edit Task</li>
                <li className="px-3 py-2 hover:bg-surface cursor-pointer text-white">Move to...</li>
                <li className="px-3 py-2 hover:bg-surface cursor-pointer text-red-400">Delete Task</li>
              </ul>
            </div>
          )}
        </div>
      </div>
      
      {/* Task labels/tags if any */}
      {task.labels && task.labels.length > 0 && (
        <div className="flex flex-wrap gap-1 mb-2">
          {task.labels.map((label, index) => (
            <span 
              key={index} 
              className={`px-2 py-0.5 text-xs rounded-full ${label.color} bg-opacity-20`}
            >
              {label.name}
            </span>
          ))}
        </div>
      )}
      
      {/* Task description preview */}
      {task.description && (
        <p className="text-xs text-text-secondary mb-3 line-clamp-2">{task.description}</p>
      )}
      
      {/* Checklist progress if any */}
      {task.checklist && task.checklist.length > 0 && (
        <div className="mb-3">
          <div className="flex items-center mb-1">
            <CheckSquare className="h-3 w-3 text-text-secondary mr-1" />
            <span className="text-xs text-text-secondary">
              {task.checklist.filter(item => item.completed).length}/{task.checklist.length}
            </span>
          </div>
          <div className="w-full bg-surface-3 rounded-full h-1.5">
            <div
              className="bg-soft-blue h-1.5 rounded-full"
              style={{ width: `${getCompletionPercentage()}%` }}
            ></div>
          </div>
        </div>
      )}
      
      {/* Task footer with metadata */}
      <div className="flex items-center justify-between text-xs text-text-secondary">
        <div className="flex items-center">
          {task.dueDate && (
            <div className="flex items-center mr-3">
              <Clock className="h-3 w-3 mr-1" />
              <span>{formatDate(task.dueDate)}</span>
            </div>
          )}
          
          {task.assignees && task.assignees.length > 0 && (
            <div className="flex items-center">
              <Users className="h-3 w-3 mr-1" />
              <span>{task.assignees.length}</span>
            </div>
          )}
        </div>
        
        {/* Milestone indicator if any */}
        {task.milestone && (
          <div className="flex items-center">
            <CheckCircle className="h-3 w-3 mr-1" />
            <span className="truncate max-w-20">{task.milestone}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskCard;
