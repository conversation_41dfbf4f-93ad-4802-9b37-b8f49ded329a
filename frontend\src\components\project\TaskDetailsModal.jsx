import { useState } from 'react';
import { 
  AlignLeft, 
  Calendar, 
  Clock, 
  Users, 
  Tag, 
  CheckSquare, 
  MessageSquare, 
  Paperclip,
  Plus,
  X,
  Edit,
  Save,
  Trash2
} from 'lucide-react';
import Modal from '../common/Modal';

const TaskDetailsModal = ({ 
  isOpen, 
  onClose, 
  task, 
  teamMembers, 
  onUpdateTask,
  onDeleteTask
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedTask, setEditedTask] = useState(task || {});
  const [newChecklistItem, setNewChecklistItem] = useState('');
  const [activeTab, setActiveTab] = useState('details');

  // If task changes, update the edited task
  if (task && task.id !== editedTask.id) {
    setEditedTask(task);
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedTask({
      ...editedTask,
      [name]: value
    });
  };

  const toggleChecklistItem = (itemId) => {
    setEditedTask(prev => ({
      ...prev,
      checklist: prev.checklist.map(item => 
        item.id === itemId ? { ...item, completed: !item.completed } : item
      )
    }));
  };

  const addChecklistItem = () => {
    if (newChecklistItem.trim() === '') return;
    
    setEditedTask(prev => ({
      ...prev,
      checklist: [
        ...(prev.checklist || []),
        { id: Date.now(), text: newChecklistItem, completed: false }
      ]
    }));
    
    setNewChecklistItem('');
  };

  const removeChecklistItem = (itemId) => {
    setEditedTask(prev => ({
      ...prev,
      checklist: prev.checklist.filter(item => item.id !== itemId)
    }));
  };

  const handleAssigneeToggle = (memberId) => {
    setEditedTask(prev => {
      const assignees = prev.assignees || [];
      const isSelected = assignees.includes(memberId);
      return {
        ...prev,
        assignees: isSelected
          ? assignees.filter(id => id !== memberId)
          : [...assignees, memberId]
      };
    });
  };

  const handleSave = () => {
    onUpdateTask(editedTask);
    setIsEditing(false);
  };

  const handleDelete = () => {
    onDeleteTask(editedTask.id);
    onClose();
  };

  // Format date to readable format
  const formatDate = (dateString) => {
    if (!dateString) return 'No date set';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Calculate completion percentage
  const getCompletionPercentage = () => {
    const checklist = editedTask.checklist || [];
    if (checklist.length === 0) return 0;
    const completed = checklist.filter(item => item.completed).length;
    return Math.round((completed / checklist.length) * 100);
  };

  // Get assignee names
  const getAssigneeNames = () => {
    if (!editedTask.assignees || editedTask.assignees.length === 0) return 'Unassigned';
    return editedTask.assignees.map(id => {
      const member = teamMembers.find(m => m.id === id);
      return member ? member.name : 'Unknown';
    }).join(', ');
  };

  if (!task) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={isEditing ? 'Edit Task' : task.title}
      size="lg"
    >
      <div className="flex flex-col h-full">
        {/* Tabs */}
        <div className="flex border-b border-border mb-4">
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 ${
              activeTab === 'details'
                ? 'border-accent-1 text-white'
                : 'border-transparent text-text-secondary hover:text-white'
            }`}
            onClick={() => setActiveTab('details')}
          >
            Details
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium border-b-2 ${
              activeTab === 'activity'
                ? 'border-accent-1 text-white'
                : 'border-transparent text-text-secondary hover:text-white'
            }`}
            onClick={() => setActiveTab('activity')}
          >
            Activity
          </button>
        </div>

        {activeTab === 'details' ? (
          <div className="space-y-4">
            {/* Title */}
            {isEditing ? (
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-white mb-1">
                  Task Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={editedTask.title}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                  required
                />
              </div>
            ) : (
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium text-white">{task.title}</h3>
                <div className="flex space-x-2">
                  <button 
                    onClick={() => setIsEditing(true)}
                    className="p-1 rounded-full hover:bg-surface-3 text-text-secondary"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button 
                    onClick={handleDelete}
                    className="p-1 rounded-full hover:bg-surface-3 text-red-400"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}

            {/* Status */}
            <div className="flex items-center">
              <span className="text-sm text-text-secondary mr-2">Status:</span>
              {isEditing ? (
                <select
                  name="status"
                  value={editedTask.status}
                  onChange={handleInputChange}
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 p-1"
                >
                  <option value="Not Started">Not Started</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Completed">Completed</option>
                </select>
              ) : (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  editedTask.status === 'Completed' ? 'bg-green-500' : 
                  editedTask.status === 'In Progress' ? 'bg-blue-500' : 
                  'bg-yellow-500'
                } bg-opacity-10 text-white`}>
                  {editedTask.status}
                </span>
              )}
            </div>

            {/* Labels */}
            {editedTask.labels && editedTask.labels.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {editedTask.labels.map((label, index) => (
                  <span 
                    key={index} 
                    className={`px-2 py-0.5 text-xs rounded-full ${label.color}`}
                  >
                    {label.name}
                  </span>
                ))}
              </div>
            )}

            {/* Description */}
            <div>
              <div className="flex items-center mb-1">
                <AlignLeft className="h-4 w-4 text-text-secondary mr-2" />
                <h4 className="text-sm font-medium text-white">Description</h4>
              </div>
              {isEditing ? (
                <textarea
                  name="description"
                  value={editedTask.description || ''}
                  onChange={handleInputChange}
                  rows="3"
                  className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2.5"
                  placeholder="Add a description..."
                ></textarea>
              ) : (
                <p className="text-sm text-text-secondary">
                  {editedTask.description || 'No description provided.'}
                </p>
              )}
            </div>

            {/* Metadata */}
            <div className="grid grid-cols-2 gap-4">
              {/* Due Date */}
              <div>
                <div className="flex items-center mb-1">
                  <Calendar className="h-4 w-4 text-text-secondary mr-2" />
                  <h4 className="text-sm font-medium text-white">Due Date</h4>
                </div>
                {isEditing ? (
                  <input
                    type="date"
                    name="dueDate"
                    value={editedTask.dueDate || ''}
                    onChange={handleInputChange}
                    className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2"
                  />
                ) : (
                  <p className="text-sm text-text-secondary">
                    {formatDate(editedTask.dueDate)}
                  </p>
                )}
              </div>

              {/* Assignees */}
              <div>
                <div className="flex items-center mb-1">
                  <Users className="h-4 w-4 text-text-secondary mr-2" />
                  <h4 className="text-sm font-medium text-white">Assignees</h4>
                </div>
                {isEditing ? (
                  <div className="bg-surface-3 border border-border rounded-lg p-2 max-h-24 overflow-y-auto">
                    {teamMembers && teamMembers.map(member => (
                      <div 
                        key={member.id}
                        className="flex items-center mb-1 last:mb-0"
                      >
                        <input
                          type="checkbox"
                          id={`edit-assignee-${member.id}`}
                          checked={(editedTask.assignees || []).includes(member.id)}
                          onChange={() => handleAssigneeToggle(member.id)}
                          className="w-3 h-3 text-accent-1 bg-surface border-border rounded focus:ring-accent-1"
                        />
                        <label
                          htmlFor={`edit-assignee-${member.id}`}
                          className="ml-2 text-xs text-white"
                        >
                          {member.name}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-text-secondary">
                    {getAssigneeNames()}
                  </p>
                )}
              </div>
            </div>

            {/* Checklist */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <CheckSquare className="h-4 w-4 text-text-secondary mr-2" />
                  <h4 className="text-sm font-medium text-white">Checklist</h4>
                </div>
                {editedTask.checklist && editedTask.checklist.length > 0 && (
                  <span className="text-xs text-text-secondary">
                    {editedTask.checklist.filter(item => item.completed).length}/{editedTask.checklist.length}
                  </span>
                )}
              </div>
              
              {/* Progress bar */}
              {editedTask.checklist && editedTask.checklist.length > 0 && (
                <div className="w-full bg-surface-3 rounded-full h-1.5 mb-3">
                  <div
                    className="bg-soft-blue h-1.5 rounded-full"
                    style={{ width: `${getCompletionPercentage()}%` }}
                  ></div>
                </div>
              )}
              
              {/* Checklist items */}
              <div className="space-y-2 mb-3">
                {editedTask.checklist && editedTask.checklist.map(item => (
                  <div 
                    key={item.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={item.completed}
                        onChange={() => toggleChecklistItem(item.id)}
                        className="w-4 h-4 text-accent-1 bg-surface border-border rounded focus:ring-accent-1 mr-2"
                      />
                      <span className={`text-sm ${item.completed ? 'text-text-secondary line-through' : 'text-white'}`}>
                        {item.text}
                      </span>
                    </div>
                    {isEditing && (
                      <button 
                        type="button"
                        onClick={() => removeChecklistItem(item.id)}
                        className="text-text-secondary hover:text-red-400"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}
              </div>
              
              {/* Add checklist item */}
              {isEditing && (
                <div className="flex items-center">
                  <input
                    type="text"
                    value={newChecklistItem}
                    onChange={(e) => setNewChecklistItem(e.target.value)}
                    placeholder="Add checklist item"
                    className="bg-surface border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full p-2 mr-2"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addChecklistItem())}
                  />
                  <button
                    type="button"
                    onClick={addChecklistItem}
                    className="p-2 bg-surface-2 hover:bg-surface text-white rounded-lg"
                  >
                    <Plus className="h-5 w-5" />
                  </button>
                </div>
              )}
            </div>

            {/* Action buttons */}
            {isEditing && (
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  type="button"
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSave}
                  className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                >
                  <Save className="h-4 w-4 mr-1" />
                  Save Changes
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <MessageSquare className="h-10 w-10 text-text-secondary mx-auto mb-2" />
            <p className="text-text-secondary">Activity tracking will be implemented in a future update.</p>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default TaskDetailsModal;
