import { createContext, useState, useContext } from 'react';

// Create the context
const UserDetailsContext = createContext();

// Create a provider component
export const UserDetailsProvider = ({ children }) => {
  const [selectedUser, setSelectedUser] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Function to open the modal with selected user
  const openUserModal = (user) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };

  // Function to close the modal
  const closeUserModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
  };

  // The value that will be provided to consumers of this context
  const value = {
    selectedUser,
    isModalOpen,
    openUserModal,
    closeUserModal
  };

  return (
    <UserDetailsContext.Provider value={value}>
      {children}
    </UserDetailsContext.Provider>
  );
};

// Custom hook to use the context
export const useUserDetails = () => {
  const context = useContext(UserDetailsContext);
  if (context === undefined) {
    throw new Error('useUserDetails must be used within a UserDetailsProvider');
  }
  return context;
};
