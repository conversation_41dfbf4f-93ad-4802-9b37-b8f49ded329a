import { useAppDispatch, useAppSelector } from '../store/hooks';
import { logout } from '../store/slices/authSlice';
import { useNavigate } from 'react-router-dom';
import { LogOut, User } from 'lucide-react';

/**
 * Example component demonstrating logout functionality
 * This shows how to implement logout in any component
 */
const LogoutExample = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);

  const handleLogout = () => {
    // Show confirmation dialog
    const confirmLogout = window.confirm('Are you sure you want to logout?');
    
    if (confirmLogout) {
      // Dispatch logout action
      dispatch(logout());
      
      // Navigate to login page
      navigate('/login');
    }
  };

  const handleQuickLogout = () => {
    // Logout without confirmation
    dispatch(logout());
    navigate('/login');
  };

  if (!isAuthenticated) {
    return (
      <div className="p-6 text-center">
        <p>You are not logged in.</p>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-md mx-auto bg-surface-2 rounded-lg">
      <h2 className="text-xl font-bold mb-4">Logout Example</h2>
      
      {/* User Info */}
      <div className="mb-6 p-4 bg-surface-3 rounded-lg">
        <div className="flex items-center mb-2">
          <div className="flex justify-center items-center w-10 h-10 rounded-full bg-blue-500 text-white text-sm font-medium mr-3">
            {user?.name ? user.name.split(' ').map(n => n[0]).join('').toUpperCase() : <User size={20} />}
          </div>
          <div>
            <p className="font-medium text-white">{user?.name || 'User'}</p>
            <p className="text-sm text-text-secondary">{user?.email || ''}</p>
          </div>
        </div>
        {user?.position && (
          <p className="text-sm text-text-secondary">Position: {user.position}</p>
        )}
      </div>

      {/* Logout Buttons */}
      <div className="space-y-3">
        <button
          onClick={handleLogout}
          className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          <LogOut size={16} className="mr-2" />
          Logout (with confirmation)
        </button>
        
        <button
          onClick={handleQuickLogout}
          className="w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
        >
          <LogOut size={16} className="mr-2" />
          Quick Logout
        </button>
      </div>

      {/* Info */}
      <div className="mt-6 p-3 bg-blue-500 bg-opacity-10 border border-blue-500 rounded-lg">
        <p className="text-sm text-blue-400">
          <strong>Note:</strong> The logout functionality:
        </p>
        <ul className="text-xs text-blue-300 mt-2 space-y-1">
          <li>• Clears Redux auth state</li>
          <li>• Removes tokens from localStorage</li>
          <li>• Redirects to login page</li>
          <li>• Works from any component</li>
        </ul>
      </div>
    </div>
  );
};

export default LogoutExample;
