import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { 
  fetchCompanies, 
  createCompany, 
  setCurrentCompany,
  clearError 
} from '../store/slices/companiesSlice';
import { 
  fetchUsers,
  inviteUser 
} from '../store/slices/usersSlice';
import { 
  fetchProjects,
  createProject 
} from '../store/slices/projectsSlice';
import { 
  checkIn,
  checkOut,
  fetchDailyOverview 
} from '../store/slices/attendanceSlice';
import LoadingSpinner from '../components/common/LoadingSpinner';

/**
 * This component demonstrates how to use Redux Toolkit with our slices
 * It shows examples of dispatching actions and reading state
 */
const ReduxUsageExample = () => {
  const dispatch = useAppDispatch();
  
  // Auth state
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);
  
  // Companies state
  const { 
    companies, 
    currentCompany, 
    loading: companiesLoading, 
    error: companiesError 
  } = useAppSelector((state) => state.companies);
  
  // Users state
  const { 
    users, 
    loading: usersLoading, 
    error: usersError 
  } = useAppSelector((state) => state.users);
  
  // Projects state
  const { 
    projects, 
    loading: projectsLoading, 
    error: projectsError 
  } = useAppSelector((state) => state.projects);
  
  // Attendance state
  const { 
    currentStatus, 
    dailyOverview,
    checkInLoading,
    checkOutLoading,
    overviewLoading 
  } = useAppSelector((state) => state.attendance);

  const [newCompanyName, setNewCompanyName] = useState('');
  const [newProjectTitle, setNewProjectTitle] = useState('');
  const [inviteEmail, setInviteEmail] = useState('');

  // Load initial data
  useEffect(() => {
    if (isAuthenticated) {
      dispatch(fetchCompanies());
      dispatch(fetchUsers());
      dispatch(fetchProjects());
      dispatch(fetchDailyOverview());
    }
  }, [dispatch, isAuthenticated]);

  // Example: Create a new company
  const handleCreateCompany = async () => {
    if (!newCompanyName.trim()) return;
    
    try {
      const result = await dispatch(createCompany({
        name: newCompanyName,
        description: `${newCompanyName} company description`,
        checkInHoursStart: "09:00",
        checkInHoursEnd: "17:00",
        messageFormat: "Daily check-in: What are you working on today?"
      }));
      
      if (createCompany.fulfilled.match(result)) {
        setNewCompanyName('');
        console.log('Company created successfully');
      }
    } catch (error) {
      console.error('Failed to create company:', error);
    }
  };

  // Example: Create a new project
  const handleCreateProject = async () => {
    if (!newProjectTitle.trim() || !currentCompany) return;
    
    try {
      const result = await dispatch(createProject({
        title: newProjectTitle,
        description: `${newProjectTitle} project description`,
        companyId: currentCompany.id,
        dueDate: "2024-12-31T23:59:59Z",
        projectFormat: "statuses"
      }));
      
      if (createProject.fulfilled.match(result)) {
        setNewProjectTitle('');
        console.log('Project created successfully');
      }
    } catch (error) {
      console.error('Failed to create project:', error);
    }
  };

  // Example: Invite a user
  const handleInviteUser = async () => {
    if (!inviteEmail.trim()) return;
    
    try {
      const result = await dispatch(inviteUser({
        email: inviteEmail,
        role: "Employee"
      }));
      
      if (inviteUser.fulfilled.match(result)) {
        setInviteEmail('');
        console.log('User invited successfully');
      }
    } catch (error) {
      console.error('Failed to invite user:', error);
    }
  };

  // Example: Check in
  const handleCheckIn = async () => {
    if (!currentCompany) return;
    
    try {
      await dispatch(checkIn({
        companyId: currentCompany.id,
        checkInMessage: "Starting work for the day"
      }));
    } catch (error) {
      console.error('Failed to check in:', error);
    }
  };

  // Example: Check out
  const handleCheckOut = async () => {
    if (!currentCompany) return;
    
    try {
      await dispatch(checkOut({
        companyId: currentCompany.id,
        checkOutMessage: "Finished work for the day",
        projectHours: []
      }));
    } catch (error) {
      console.error('Failed to check out:', error);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="p-6 text-center">
        <p>Please log in to see Redux examples</p>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-6xl mx-auto space-y-8">
      <h1 className="text-3xl font-bold">Redux Toolkit Usage Examples</h1>
      
      {/* User Info */}
      <div className="bg-surface-2 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">Current User</h2>
        <p>Name: {user?.name}</p>
        <p>Email: {user?.email}</p>
        <p>Position: {user?.position}</p>
      </div>

      {/* Companies Section */}
      <div className="bg-surface-2 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Companies</h2>
        
        {companiesLoading && <LoadingSpinner />}
        {companiesError && (
          <div className="text-red-500 mb-4">Error: {companiesError}</div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {companies.map((company) => (
            <div 
              key={company.id} 
              className={`p-3 border rounded cursor-pointer ${
                currentCompany?.id === company.id ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
              }`}
              onClick={() => dispatch(setCurrentCompany(company))}
            >
              <h3 className="font-medium">{company.name}</h3>
              <p className="text-sm text-gray-600">{company.description}</p>
            </div>
          ))}
        </div>
        
        <div className="flex gap-2">
          <input
            type="text"
            value={newCompanyName}
            onChange={(e) => setNewCompanyName(e.target.value)}
            placeholder="New company name"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleCreateCompany}
            disabled={companiesLoading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            Create Company
          </button>
        </div>
      </div>

      {/* Projects Section */}
      <div className="bg-surface-2 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Projects</h2>
        
        {projectsLoading && <LoadingSpinner />}
        {projectsError && (
          <div className="text-red-500 mb-4">Error: {projectsError}</div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          {projects.map((project) => (
            <div key={project.id} className="p-3 border rounded">
              <h3 className="font-medium">{project.title}</h3>
              <p className="text-sm text-gray-600">{project.description}</p>
              <p className="text-xs text-gray-500">Due: {new Date(project.dueDate).toLocaleDateString()}</p>
            </div>
          ))}
        </div>
        
        <div className="flex gap-2">
          <input
            type="text"
            value={newProjectTitle}
            onChange={(e) => setNewProjectTitle(e.target.value)}
            placeholder="New project title"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleCreateProject}
            disabled={projectsLoading || !currentCompany}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            Create Project
          </button>
        </div>
      </div>

      {/* Attendance Section */}
      <div className="bg-surface-2 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Attendance</h2>
        
        <div className="mb-4">
          <p>Current Status: {currentStatus || 'Not checked in'}</p>
          {dailyOverview && (
            <div className="mt-2">
              <p>Today's Hours: {dailyOverview.totalHours || 0}</p>
              <p>Check-ins: {dailyOverview.checkIns || 0}</p>
            </div>
          )}
        </div>
        
        <div className="flex gap-2">
          <button
            onClick={handleCheckIn}
            disabled={checkInLoading || currentStatus === 'checked-in'}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {checkInLoading ? <LoadingSpinner size={16} /> : 'Check In'}
          </button>
          <button
            onClick={handleCheckOut}
            disabled={checkOutLoading || currentStatus !== 'checked-in'}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
          >
            {checkOutLoading ? <LoadingSpinner size={16} /> : 'Check Out'}
          </button>
        </div>
      </div>

      {/* Users Section */}
      <div className="bg-surface-2 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Users</h2>
        
        {usersLoading && <LoadingSpinner />}
        {usersError && (
          <div className="text-red-500 mb-4">Error: {usersError}</div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {users.map((user) => (
            <div key={user.id} className="p-3 border rounded">
              <h3 className="font-medium">{user.name}</h3>
              <p className="text-sm text-gray-600">{user.email}</p>
              <p className="text-xs text-gray-500">{user.position}</p>
            </div>
          ))}
        </div>
        
        <div className="flex gap-2">
          <input
            type="email"
            value={inviteEmail}
            onChange={(e) => setInviteEmail(e.target.value)}
            placeholder="Email to invite"
            className="flex-1 p-2 border rounded"
          />
          <button
            onClick={handleInviteUser}
            disabled={usersLoading}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Invite User
          </button>
        </div>
      </div>
    </div>
  );
};

export default ReduxUsageExample;
