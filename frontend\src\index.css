@import "tailwindcss";

@layer base {
  @theme {
    /* Dark theme colors */
    --color-background: #0a0a0a;
    --color-surface: #141414;
    --color-surface-2: #1a1a1a;
    --color-surface-3: #222222;
    --color-primary: #f2f2f2;
    --color-secondary: #a0a0a0;
    --color-text-primary: #f0f0f0;
    --color-text-secondary: #9e9e9e;
    --color-border: #2a2a2a;

    /* Accent colors - softer palette */
    --color-accent-1: #e2e2e2; /* Primary accent (white shade) */
    --color-accent-2: #6bb893; /* Success/CheckSync - softer green */
    --color-accent-3: #e6c27a; /* Warning/Beam - softer yellow */
    --color-accent-4: #e57373; /* Error/OffDoff - softer red */

    /* Soft colors for UI elements */
    --color-soft-blue: #7a89c2;
    --color-soft-purple: #a78bc9;
    --color-soft-teal: #68b0ab;
    --color-soft-pink: #d698b9;

    /* Gradients */
    --gradient-dark-1: linear-gradient(135deg, #141414 0%, #1a1a1a 100%);
    --gradient-dark-2: linear-gradient(135deg, #1a1a1a 0%, #222222 100%);
    --gradient-dark-3: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0.03) 0%,
      rgba(255, 255, 255, 0.01) 100%
    );
    --gradient-card: linear-gradient(
      145deg,
      rgba(26, 26, 26, 0.9) 0%,
      rgba(18, 18, 18, 0.8) 100%
    );

    /* Glassmorphism */
    --glass-bg: rgba(25, 25, 25, 0.6);
    --glass-border: rgba(255, 255, 255, 0.08);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    /* Font */
    --font-main: "Inter", "sans-serif";
  }
}

@layer utilities {
  .glass {
    background: var(--glass-bg);
    backdrop-filter: blur(8px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: 0.75rem;
    position: relative;
    overflow: hidden;
  }

  .profile-card {
    background: var(--glass-bg);
    backdrop-filter: blur(12px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: 0.75rem;
    position: abbsolute;
    overflow: hidden;
  }

  .glass-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.12) 50%,
      rgba(255, 255, 255, 0) 100%
    );
  }

  .gradient-card {
    background: var(--gradient-card);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: 0.75rem;
    position: relative;
    overflow: hidden;
  }

  .neomorphic {
    background: var(--color-surface-2);
    border-radius: 0.5rem;
    box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.5),
      -5px -5px 10px rgba(255, 255, 255, 0.03);
  }

  .gradient-dark-1 {
    background: var(--gradient-dark-1);
  }

  .gradient-dark-2 {
    background: var(--gradient-dark-2);
  }

  .gradient-dark-3 {
    background: var(--gradient-dark-3);
  }

  .subtle-glow {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.03);
  }

  .input-dark {
    background-color: var(--color-surface);
    border: 1px solid var(--color-border);
    color: var(--color-text-primary);
    transition: all 0.2s ease;
  }

  .input-dark:focus {
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  .btn-white {
    background: rgba(255, 255, 255, 0.9);
    color: #111;
    border: none;
    transition: all 0.2s ease;
  }

  .btn-white:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
  }

  .btn-white-outline {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
  }

  .btn-white-outline:hover {
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
  }

  .btn-white-ghost {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
  }

  .btn-white-ghost:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

body {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-main);
}

/* Custom Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #3a3a3a;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-surface);
}

/* For dropdown menus and other scrollable areas */
.scrollable-area {
  scrollbar-width: thin;
  scrollbar-color: var(--color-border) var(--color-surface);
}

.scrollable-area::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollable-area::-webkit-scrollbar-track {
  background: var(--color-surface);
  border-radius: 3px;
}

.scrollable-area::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

.scrollable-area::-webkit-scrollbar-thumb:hover {
  background: #3a3a3a;
}
