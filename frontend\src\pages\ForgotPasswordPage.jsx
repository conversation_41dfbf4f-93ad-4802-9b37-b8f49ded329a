import { useState } from 'react';
import { Mail, ArrowLeft, AlertCircle } from 'lucide-react';
import { Link } from 'react-router-dom';
import { authService } from '../api';
import LoadingSpinner from '../components/common/LoadingSpinner';

const ForgotPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    if (e.preventDefault) e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Call the forgot password API
      const response = await authService.forgotPassword(email);

      // If request is successful, show success message
      if (response.success) {
        setIsSubmitted(true);
      } else {
        // Handle unsuccessful request but with a 200 response
        setError(response.message || "Failed to send reset email. Please try again.");
      }
    } catch (error) {
      // Handle error
      setError(
        error.response?.data?.message ||
        "An error occurred. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background relative overflow-hidden">
      {/* Background elements for glassmorphism effect */}
      <div className="absolute w-72 h-72 rounded-full bg-soft-purple opacity-5 blur-3xl -top-20 -left-20"></div>
      <div className="absolute w-96 h-96 rounded-full bg-soft-teal opacity-5 blur-3xl -bottom-20 -right-20"></div>

      <div className="glass-card p-8 w-full max-w-md z-10">
        <div className="mb-6">
          <Link to="/login" className="inline-flex items-center text-text-secondary hover:text-white text-sm">
            <ArrowLeft size={16} className="mr-2" />
            Back to login
          </Link>
        </div>

        {!isSubmitted ? (
          <>
            <div className="text-center mb-8">
              <h1 className="text-2xl font-semibold text-white mb-2">
                Forgot Password
              </h1>
              <p className="text-text-secondary text-sm">
                Enter your email and we'll send you a link to reset your password
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-white mb-1"
                >
                  Email Address
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail size={16} className="text-soft-blue opacity-70" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="input-dark block w-full pl-10 pr-3 py-2.5 rounded-lg text-white text-sm"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded-lg flex items-center mb-4">
                  <AlertCircle size={16} className="mr-2" />
                  <span>{error}</span>
                </div>
              )}

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2.5 px-4 btn-white font-medium rounded-lg text-sm flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size={20} color="#4F46E5" className="mr-2" />
                    Sending...
                  </>
                ) : (
                  "Send Reset Link"
                )}
              </button>
            </form>
          </>
        ) : (
          <div className="text-center py-8">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-surface-3 mb-4">
              <Mail size={24} className="text-soft-blue" />
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">Check Your Email</h2>
            <p className="text-text-secondary mb-6">
              We've sent a password reset link to <span className="text-white">{email}</span>
            </p>
            <p className="text-text-secondary text-sm mb-6">
              Didn't receive the email? Check your spam folder or
            </p>
            {error && (
              <div className="bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded-lg flex items-center mb-4">
                <AlertCircle size={16} className="mr-2" />
                <span>{error}</span>
              </div>
            )}

            <button
              onClick={() => handleSubmit({ preventDefault: () => {} })}
              disabled={isLoading}
              className="text-soft-blue hover:text-white transition-colors text-sm font-medium flex items-center justify-center mx-auto"
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size={16} color="#4F46E5" className="mr-2" />
                  Resending...
                </>
              ) : (
                "Click here to resend"
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
