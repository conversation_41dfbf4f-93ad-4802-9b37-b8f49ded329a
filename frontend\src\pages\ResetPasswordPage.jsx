import { useState, useEffect } from 'react';
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle } from 'lucide-react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { authService } from '../api';
import LoadingSpinner from '../components/common/LoadingSpinner';

const ResetPasswordPage = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [validationError, setValidationError] = useState('');
  const [apiError, setApiError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Redirect if no token is provided
  useEffect(() => {
    if (!token) {
      navigate('/login');
    }
  }, [token, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setValidationError('');
    setApiError(null);

    // Validate passwords match
    if (formData.password !== formData.confirmPassword) {
      setValidationError('Passwords do not match');
      return;
    }

    // Validate password strength
    if (formData.password.length < 8) {
      setValidationError('Password must be at least 8 characters long');
      return;
    }

    setIsLoading(true);

    try {
      // Call the reset password API
      const response = await authService.resetPassword({
        token,
        newPassword: formData.password,
      });

      // If request is successful, show success message
      if (response.success) {
        setIsSubmitted(true);
      } else {
        // Handle unsuccessful request but with a 200 response
        setApiError(response.message || "Failed to reset password. Please try again.");
      }
    } catch (error) {
      // Handle error
      setApiError(
        error.response?.data?.message ||
        "An error occurred. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background relative overflow-hidden">
      {/* Background elements for glassmorphism effect */}
      <div className="absolute w-72 h-72 rounded-full bg-soft-purple opacity-5 blur-3xl -top-20 -left-20"></div>
      <div className="absolute w-96 h-96 rounded-full bg-soft-teal opacity-5 blur-3xl -bottom-20 -right-20"></div>

      <div className="glass-card p-8 w-full max-w-md z-10">
        {!isSubmitted ? (
          <>
            <div className="text-center mb-8">
              <h1 className="text-2xl font-semibold text-white mb-2">
                Reset Your Password
              </h1>
              <p className="text-text-secondary text-sm">
                Create a new password for your account
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Validation Error */}
              {validationError && (
                <div className="bg-accent-4 bg-opacity-10 text-accent-4 px-4 py-2 rounded-lg text-sm flex items-center">
                  <AlertCircle size={16} className="mr-2" />
                  {validationError}
                </div>
              )}

              {/* API Error */}
              {apiError && (
                <div className="bg-red-500 bg-opacity-10 border border-red-500 text-red-500 px-4 py-3 rounded-lg flex items-center">
                  <AlertCircle size={16} className="mr-2" />
                  <span>{apiError}</span>
                </div>
              )}

              <div className="space-y-4">
                {/* New Password Input */}
                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    New Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock size={16} className="text-soft-purple opacity-70" />
                    </div>
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                      required
                      value={formData.password}
                      onChange={handleChange}
                      className="input-dark block w-full pl-10 pr-10 py-2.5 rounded-lg text-white text-sm"
                      placeholder="••••••••"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="text-soft-purple opacity-70 hover:opacity-100 focus:outline-none"
                      >
                        {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                    </div>
                  </div>
                  <p className="mt-1 text-xs text-text-secondary">
                    Must be at least 8 characters
                  </p>
                </div>

                {/* Confirm Password Input */}
                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Confirm Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Lock size={16} className="text-soft-purple opacity-70" />
                    </div>
                    <input
                      id="confirmPassword"
                      name="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      required
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      className="input-dark block w-full pl-10 pr-10 py-2.5 rounded-lg text-white text-sm"
                      placeholder="••••••••"
                    />
                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="text-soft-purple opacity-70 hover:opacity-100 focus:outline-none"
                      >
                        {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full py-2.5 px-4 btn-white font-medium rounded-lg text-sm flex items-center justify-center"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner size={20} color="#4F46E5" className="mr-2" />
                    Resetting Password...
                  </>
                ) : (
                  "Reset Password"
                )}
              </button>
            </form>
          </>
        ) : (
          <div className="text-center py-8">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-surface-3 mb-4">
              <CheckCircle size={24} className="text-accent-2" />
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">Password Reset Complete</h2>
            <p className="text-text-secondary mb-6">
              Your password has been successfully reset
            </p>
            <Link
              to="/login"
              className="w-full py-2.5 px-4 btn-white font-medium rounded-lg text-sm inline-block"
            >
              Back to Login
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResetPasswordPage;
