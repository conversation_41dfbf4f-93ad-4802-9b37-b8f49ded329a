import { Clock, Calendar, Users } from 'lucide-react';
import AttendanceCalendar from '../../components/attendance/AttendanceCalendar';

const AttendancePage = () => {
  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Attendance</h2>
          <p className="mt-1 text-sm text-text-secondary">
            Track team attendance and check-ins
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-text-secondary">Today: </span>
            <span className="text-sm text-white">{new Date().toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Calendar view */}
      <AttendanceCalendar />
    </div>
  );
};

export default AttendancePage;
