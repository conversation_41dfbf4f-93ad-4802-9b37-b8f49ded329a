import {
  Users,
  Clock,
  Calendar,
  CheckCircle,
  CheckSquare,
  TriangleAlert,
  ArrowUpRight,
  Star,
  CalendarDays,
  Award,
  Briefcase,
  Plus,
} from "lucide-react";
import { Link } from "react-router-dom";
import { useState } from "react";
import CreateProjectModal from "../../components/dashboard/CreateProjectModal";

const DashboardPage = () => {
  // State for active tabs
  const [activePerformanceTab, setActivePerformanceTab] = useState("upcoming");
  const [activeLeavesTab, setActiveLeavesTab] = useState("upcoming");

  // State for project modal
  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);

  // Handler for opening the project modal
  const openCreateProjectModal = () => {
    setIsCreateProjectModalOpen(true);
  };

  // Handler for closing the project modal
  const closeCreateProjectModal = () => {
    setIsCreateProjectModalOpen(false);
  };

  // Handler for creating a new project
  const handleCreateProject = (project) => {
    console.log('New project created:', project);
    // Here you would typically save the project to your backend
  };
  // Mock data for dashboard
  const stats = [
    {
      id: 1,
      name: "Team Members",
      value: "15",
      icon: Users,
      color: "bg-soft-blue bg-opacity-10",
      textColor: "text-soft-blue",
      link: "/dashboard/employees",
    },
    {
      id: 2,
      name: "Present Today",
      value: "12",
      icon: CheckCircle,
      color: "bg-accent-2 bg-opacity-10",
      textColor: "text-accent-2",
      link: "/dashboard/attendance/overview",
    },
    {
      id: 3,
      name: "On Leave",
      value: "2",
      color: "bg-soft-purple bg-opacity-10",
      icon: Calendar,
      textColor: "text-soft-purple",
      link: "/dashboard/leaves",
    },
    {
      id: 4,
      name: "Tasks Due Today",
      value: "8",
      icon: CheckSquare,
      color: "bg-soft-green bg-opacity-10",
      textColor: "text-soft-green",
      link: "/dashboard/projects",
    },
    {
      id: 5,
      name: "Overdue Tasks",
      value: "3",
      icon: TriangleAlert,
      color: "bg-accent-4 bg-opacity-10",
      textColor: "text-accent-4",
      link: "/dashboard/projects",
    },
  ];

  // Mock data for check-ins
  const checkIns = [
    {
      id: 1,
      name: "John Doe",
      time: "09:00 AM",
      message: "Working on UI design for the dashboard",
      avatar: "JD",
    },
    {
      id: 2,
      name: "Jane Smith",
      time: "09:15 AM",
      message: "Backend API development",
      avatar: "JS",
    },
    {
      id: 3,
      name: "Mike Johnson",
      time: "08:45 AM",
      message: "Testing the new features",
      avatar: "MJ",
    },
    {
      id: 4,
      name: "Sarah Williams",
      time: "09:30 AM",
      message: "Client meeting at 11 AM",
      avatar: "SW",
    },
  ];

  // Mock data for projects
  const activities = [
    {
      id: 1,
      title: "Website Redesign",
      status: "In Progress",
      assignees: 4,
      completion: 65,
    },
    {
      id: 2,
      title: "Mobile App Development",
      status: "Not Started",
      assignees: 3,
      completion: 0,
    },
    {
      id: 3,
      title: "API Integration",
      status: "In Progress",
      assignees: 2,
      completion: 40,
    },
    {
      id: 4,
      title: "Database Migration",
      status: "Completed",
      assignees: 2,
      completion: 100,
    },
  ];

  // Mock data for upcoming performance evaluations
  const upcomingEvaluations = [
    {
      id: 1,
      employee: "John Doe",
      position: "Frontend Developer",
      date: "May 15, 2023",
      evaluator: "Sarah Williams",
      avatar: "JD",
    },
    {
      id: 2,
      employee: "Jane Smith",
      position: "Project Manager",
      date: "May 18, 2023",
      evaluator: "Mike Johnson",
      avatar: "JS",
    },
    {
      id: 3,
      employee: "Alex Kim",
      position: "DevOps Engineer",
      date: "May 20, 2023",
      evaluator: "Sarah Williams",
      avatar: "AK",
    },
  ];

  // Mock data for recent performance evaluations
  const recentEvaluations = [
    {
      id: 1,
      employee: "Tom Wilson",
      position: "Product Manager",
      date: "April 28, 2023",
      score: 4.5,
      avatar: "TW",
    },
    {
      id: 2,
      employee: "Rachel Lee",
      position: "QA Engineer",
      date: "April 25, 2023",
      score: 4.2,
      avatar: "RL",
    },
    {
      id: 3,
      employee: "Brian Kim",
      position: "Data Scientist",
      date: "April 20, 2023",
      score: 4.7,
      avatar: "BK",
    },
  ];

  // Mock data for upcoming leaves
  const upcomingLeaves = [
    {
      id: 1,
      employee: "Mike Johnson",
      position: "Backend Developer",
      startDate: "May 10, 2023",
      endDate: "May 15, 2023",
      type: "Vacation",
      avatar: "MJ",
    },
    {
      id: 2,
      employee: "Sarah Williams",
      position: "UX Designer",
      startDate: "May 20, 2023",
      endDate: "May 22, 2023",
      type: "Personal",
      avatar: "SW",
    },
    {
      id: 3,
      employee: "Chris Park",
      position: "Full Stack Developer",
      startDate: "May 25, 2023",
      endDate: "May 26, 2023",
      type: "Sick Leave",
      avatar: "CP",
    },
  ];

  // Mock data for requested leaves
  const requestedLeaves = [
    {
      id: 1,
      employee: "David Miller",
      position: "Marketing Specialist",
      startDate: "June 5, 2023",
      endDate: "June 9, 2023",
      type: "Vacation",
      status: "Pending",
      avatar: "DM",
    },
    {
      id: 2,
      employee: "Jane Smith",
      position: "Project Manager",
      startDate: "June 15, 2023",
      endDate: "June 16, 2023",
      type: "Personal",
      status: "Pending",
      avatar: "JS",
    },
    {
      id: 3,
      employee: "Alex Kim",
      position: "DevOps Engineer",
      startDate: "June 20, 2023",
      endDate: "June 30, 2023",
      type: "Vacation",
      status: "Pending",
      avatar: "AK",
    },
  ];

  // Helper function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Done":
      case "Completed":
        return "bg-green-500";
      case "In Progress":
        return "bg-blue-500";
      case "Not Started":
        return "bg-yellow-500";
      default:
        return "bg-text-secondary";
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        {stats.map((stat) => (
          <div key={stat.id} className="glass-card p-5 rounded-lg relative">
            {/* Arrow icon in top right corner */}
            <Link
              to={stat.link}
              className="absolute top-3 right-3 p-1.5 rounded-full bg-surface-3 hover:bg-surface-2 transition-colors"
              title={`View ${stat.name}`}
            >
              <ArrowUpRight className={`h-4 w-4 ${stat.textColor}`} />
            </Link>

            <div className="flex items-center">
              <div className={`p-3 rounded-lg`}>
                <stat.icon className={`h-6 w-6 ${stat.textColor}`} />
              </div>
              <div className="ml-3">
                <p className="text-xs font-medium text-text-secondary">
                  {stat.name}
                </p>
                <p className="text-2xl font-semibold text-white">
                  {stat.value}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Today's Check-ins */}
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">
              Today's Check-ins
            </h3>
            <Clock className="h-5 w-5 text-text-secondary" />
          </div>
          <div className="p-5 space-y-4">
            {checkIns.map((checkIn) => (
              <div key={checkIn.id} className="flex items-start">
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center text-white text-sm font-medium">
                    {checkIn.avatar}
                  </div>
                </div>
                <div className="ml-3 flex-1">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-white">
                      {checkIn.name}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {checkIn.time}
                    </p>
                  </div>
                  <p className="text-sm text-text-secondary mt-1">
                    {checkIn.message}
                  </p>
                </div>
              </div>
            ))}
            <button className="w-full py-2 text-sm text-soft-blue hover:text-white transition-colors">
              View all check-ins
            </button>
          </div>
        </div>

        {/* Projects Card */}
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">
              Projects
            </h3>
            <button
              className="px-4 py-1.5 text-sm bg-surface-3 text-white rounded-lg hover:bg-surface flex items-center shadow-surface shadow-md"
              onClick={openCreateProjectModal}
            >
              <Plus className="h-4 w-4 mr-1" />
              New Project
            </button>
          </div>
          <div className="p-5 space-y-4">
            {activities.slice(0, 3).map((activity) => (
              <div key={activity.id} className="p-3 rounded-lg bg-surface-3 bg-opacity-50 hover:bg-opacity-70 transition-colors">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium text-white text-sm">
                    {activity.title}
                  </h4>
                  <span
                    className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                      activity.status
                    )} bg-opacity-10 text-white`}
                  >
                    {activity.status}
                  </span>
                </div>
                <div className="flex justify-between items-center text-xs text-text-secondary mt-2">
                  <div className="flex items-center">
                    <Users className="mr-1 w-3 h-3" />
                    <span>{activity.assignees} members</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-16 bg-surface-3 rounded-full h-1.5 mr-1">
                      <div
                        className="bg-soft-blue h-1.5 rounded-full"
                        style={{ width: `${activity.completion}%` }}
                      ></div>
                    </div>
                    <span>{activity.completion}%</span>
                  </div>
                </div>
              </div>
            ))}
            <Link to="/dashboard/projects" className="block w-full text-center py-2 text-sm text-soft-blue hover:text-white transition-colors">
              View all projects
            </Link>
          </div>
        </div>
      </div>

      {/* Two cards grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Performance Evaluation Card */}
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Performance Evaluation</h3>
            <Award className="h-5 w-5 text-text-secondary" />
          </div>

          {/* Tabs */}
          <div className="flex border-b border-border">
            <button
              className={`flex-1 py-2 text-sm font-medium text-center ${
                activePerformanceTab === "upcoming"
                  ? "text-soft-blue border-b-2 border-soft-blue"
                  : "text-text-secondary hover:text-white"
              }`}
              onClick={() => setActivePerformanceTab("upcoming")}
            >
              Upcoming Evaluations
            </button>
            <button
              className={`flex-1 py-2 text-sm font-medium text-center ${
                activePerformanceTab === "recent"
                  ? "text-soft-blue border-b-2 border-soft-blue"
                  : "text-text-secondary hover:text-white"
              }`}
              onClick={() => setActivePerformanceTab("recent")}
            >
              Recent Evaluations
            </button>
          </div>

          {/* Tab Content */}
          <div className="p-4">
            {activePerformanceTab === "upcoming" ? (
              <div className="space-y-3">
                {upcomingEvaluations.map((evaluation) => (
                  <div key={evaluation.id} className="p-3 rounded-lg bg-surface-3 bg-opacity-50 hover:bg-opacity-70 transition-colors">
                    <div className="flex items-start">
                      <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center text-white text-sm font-medium mr-3">
                        {evaluation.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="text-sm font-medium text-white">{evaluation.employee}</p>
                          <p className="text-xs text-text-secondary">{evaluation.date}</p>
                        </div>
                        <p className="text-xs text-text-secondary mt-1">{evaluation.position}</p>
                        <p className="text-xs text-text-secondary mt-2">
                          <span className="text-soft-blue">Evaluator:</span> {evaluation.evaluator}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <Link to="/dashboard/performance" className="block w-full text-center py-2 text-sm text-soft-blue hover:text-white transition-colors">
                  View all evaluations
                </Link>
              </div>
            ) : (
              <div className="space-y-3">
                {recentEvaluations.map((evaluation) => (
                  <div key={evaluation.id} className="p-3 rounded-lg bg-surface-3 bg-opacity-50 hover:bg-opacity-70 transition-colors">
                    <div className="flex items-start">
                      <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center text-white text-sm font-medium mr-3">
                        {evaluation.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="text-sm font-medium text-white">{evaluation.employee}</p>
                          <div className="flex items-center">
                            <Star className="h-3 w-3 text-accent-2 mr-1" />
                            <span className="text-xs font-medium text-accent-2">{evaluation.score}</span>
                          </div>
                        </div>
                        <p className="text-xs text-text-secondary mt-1">{evaluation.position}</p>
                        <p className="text-xs text-text-secondary mt-2">
                          <span className="text-soft-blue">Completed:</span> {evaluation.date}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <Link to="/dashboard/performance" className="block w-full text-center py-2 text-sm text-soft-blue hover:text-white transition-colors">
                  View all evaluations
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Leaves Card */}
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Leaves</h3>
            <CalendarDays className="h-5 w-5 text-text-secondary" />
          </div>

          {/* Tabs */}
          <div className="flex border-b border-border">
            <button
              className={`flex-1 py-2 text-sm font-medium text-center ${
                activeLeavesTab === "upcoming"
                  ? "text-soft-blue border-b-2 border-soft-blue"
                  : "text-text-secondary hover:text-white"
              }`}
              onClick={() => setActiveLeavesTab("upcoming")}
            >
              Upcoming Leaves
            </button>
            <button
              className={`flex-1 py-2 text-sm font-medium text-center ${
                activeLeavesTab === "requested"
                  ? "text-soft-blue border-b-2 border-soft-blue"
                  : "text-text-secondary hover:text-white"
              }`}
              onClick={() => setActiveLeavesTab("requested")}
            >
              Requested Leaves
            </button>
          </div>

          {/* Tab Content */}
          <div className="p-4">
            {activeLeavesTab === "upcoming" ? (
              <div className="space-y-3">
                {upcomingLeaves.map((leave) => (
                  <div key={leave.id} className="p-3 rounded-lg bg-surface-3 bg-opacity-50 hover:bg-opacity-70 transition-colors">
                    <div className="flex items-start">
                      <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center text-white text-sm font-medium mr-3">
                        {leave.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="text-sm font-medium text-white">{leave.employee}</p>
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-soft-purple bg-opacity-10 text-white">
                            {leave.type}
                          </span>
                        </div>
                        <p className="text-xs text-text-secondary mt-1">{leave.position}</p>
                        <p className="text-xs text-text-secondary mt-2">
                          <span className="text-soft-blue">Duration:</span> {leave.startDate} - {leave.endDate}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <Link to="/dashboard/leaves" className="block w-full text-center py-2 text-sm text-soft-blue hover:text-white transition-colors">
                  View all leaves
                </Link>
              </div>
            ) : (
              <div className="space-y-3">
                {requestedLeaves.map((leave) => (
                  <div key={leave.id} className="p-3 rounded-lg bg-surface-3 bg-opacity-50 hover:bg-opacity-70 transition-colors">
                    <div className="flex items-start">
                      <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center text-white text-sm font-medium mr-3">
                        {leave.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <p className="text-sm font-medium text-white">{leave.employee}</p>
                          <div className="flex space-x-2">
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-soft-purple bg-opacity-10 text-white">
                              {leave.type}
                            </span>
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-accent-4 bg-opacity-10 text-white">
                              {leave.status}
                            </span>
                          </div>
                        </div>
                        <p className="text-xs text-text-secondary mt-1">{leave.position}</p>
                        <p className="text-xs text-text-secondary mt-2">
                          <span className="text-soft-blue">Requested:</span> {leave.startDate} - {leave.endDate}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                <Link to="/dashboard/leaves" className="block w-full text-center py-2 text-sm text-soft-blue hover:text-white transition-colors">
                  View all requests
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateProjectModalOpen}
        onClose={closeCreateProjectModal}
        onCreateProject={handleCreateProject}
      />
    </div>
  );
};

export default DashboardPage;
