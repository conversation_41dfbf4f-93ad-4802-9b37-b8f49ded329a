import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Mail,
  Calendar,
  Briefcase,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Clock,
  FileText,
  BarChart,
} from "lucide-react";
import FilterDropdown from "../../components/common/FilterDropdown";
import DateRangePicker from "../../components/common/DateRangePicker";
import Pagination from "../../components/common/Pagination";
import EditEmployeeModal from "../../components/employees/EditEmployeeModal";

// Mock employee data (focused on relevant details for the app)
const mockEmployeeDetails = {
  1: {
    id: 1,
    name: "<PERSON>",
    avatar: "/avatars/avatar-1.jpg",
    position: "Senior Developer",
    role: "Admin",
    dateJoined: "2022-01-15",
    department: "Engineering",
    email: "<EMAIL>",
    bio: "Experienced full-stack developer with 8+ years of experience in building scalable web applications.",
    employeeId: "EMP-001",
    status: "Active",
    manager: "<PERSON>",
  },
  2: {
    id: 2,
    name: "<PERSON>",
    avatar: "/avatars/avatar-2.jpg",
    position: "UI/UX Designer",
    role: "User",
    dateJoined: "2022-03-10",
    department: "Design",
    email: "<EMAIL>",
    bio: "Creative designer with a passion for user-centered design and accessibility.",
    employeeId: "EMP-002",
    status: "Active",
    manager: "Robert Johnson",
  },
  3: {
    id: 3,
    name: "Robert Johnson",
    avatar: "/avatars/avatar-3.jpg",
    position: "Project Manager",
    role: "Manager",
    dateJoined: "2021-11-05",
    department: "Product",
    email: "<EMAIL>",
    bio: "Experienced project manager with a track record of delivering successful projects on time and within budget.",
    employeeId: "EMP-003",
    status: "Active",
    manager: "Michael Wilson",
  },
};

// Mock activity data
const mockActivity = [
  {
    id: 1,
    type: "check_in",
    description: "Checked in at 9:02 AM",
    date: "2023-07-10T09:02:00",
    message: "Working on user authentication module",
  },
  {
    id: 2,
    type: "check_out",
    description: "Checked out at 5:30 PM",
    date: "2023-07-10T17:30:00",
    message: "Completed user authentication module",
  },
  {
    id: 3,
    type: "leave_request",
    description: "Requested 3 days of vacation leave",
    date: "2023-07-08T09:15:00",
    status: "Approved",
  },
  {
    id: 4,
    type: "check_in",
    description: "Checked in at 9:05 AM",
    date: "2023-07-07T09:05:00",
    message: "Working on API integration",
  },
  {
    id: 5,
    type: "check_out",
    description: "Checked out at 5:45 PM",
    date: "2023-07-07T17:45:00",
    message: "Completed API integration testing",
  },
];

// Mock project activities data
const mockTeamTasks = [
  {
    id: 1,
    title: "User Authentication Module",
    description: "Implement secure user authentication system",
    status: "Completed",
    startDate: "2023-07-01",
    endDate: "2023-07-10",
    progress: 100,
  },
  {
    id: 2,
    title: "Dashboard Redesign",
    description: "Redesign the main dashboard for better UX",
    status: "In Progress",
    startDate: "2023-07-11",
    endDate: "2023-07-20",
    progress: 60,
  },
  {
    id: 3,
    title: "API Integration",
    description: "Integrate with third-party payment API",
    status: "Not Started",
    startDate: "2023-07-21",
    endDate: "2023-07-30",
    progress: 0,
  },
];

// Mock leaves data
const mockLeaves = [
  {
    id: 1,
    type: "Vacation",
    startDate: "2023-08-15",
    endDate: "2023-08-20",
    duration: "5 days",
    status: "Approved",
    approvedBy: "Sarah Brown",
    requestedOn: "2023-07-20",
  },
  {
    id: 2,
    type: "Sick",
    startDate: "2023-06-05",
    endDate: "2023-06-06",
    duration: "2 days",
    status: "Approved",
    approvedBy: "Sarah Brown",
    requestedOn: "2023-06-05",
  },
  {
    id: 3,
    type: "Personal",
    startDate: "2023-09-10",
    endDate: "2023-09-10",
    duration: "1 day",
    status: "Pending",
    approvedBy: null,
    requestedOn: "2023-07-25",
  },
];

// Mock attendance data
const mockAttendance = [
  {
    date: "2023-07-10",
    status: "Present",
    checkIn: "09:02",
    checkOut: "17:30",
  },
  { date: "2023-07-09", status: "Weekend", checkIn: null, checkOut: null },
  { date: "2023-07-08", status: "Weekend", checkIn: null, checkOut: null },
  {
    date: "2023-07-07",
    status: "Present",
    checkIn: "08:55",
    checkOut: "17:45",
  },
  {
    date: "2023-07-06",
    status: "Present",
    checkIn: "09:10",
    checkOut: "18:00",
  },
  {
    date: "2023-07-05",
    status: "Present",
    checkIn: "09:05",
    checkOut: "17:30",
  },
  {
    date: "2023-07-04",
    status: "Present",
    checkIn: "08:50",
    checkOut: "17:15",
  },
  { date: "2023-07-03", status: "On Leave", checkIn: null, checkOut: null },
];

// Mock performance data
const mockPerformance = [
  {
    id: 1,
    taskTitle: "User Management System",
    date: "2023-04-15",
    rating: 4.8,
    feedback:
      "Excellent work on implementing the authentication system. Code was clean and well-documented.",
  },
  {
    id: 2,
    taskTitle: "API Integration",
    date: "2023-02-20",
    rating: 4.5,
    feedback:
      "Good job on the API integration. Some minor issues with error handling, but overall solid work.",
  },
];

const EmployeeDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");
  const [showEditModal, setShowEditModal] = useState(false);
  const [filterOption, setFilterOption] = useState("Today");
  const [dateRange, setDateRange] = useState({
    startDate: null,
    endDate: null,
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [attendanceData, setAttendanceData] = useState(mockAttendance);
  const [leavesData, setLeavesData] = useState(mockLeaves);
  const [projectsData, setProjectsData] = useState(mockTeamTasks);

  // Filter options
  const filterOptions = [
    "Today",
    "Yesterday",
    "This Week",
    "This Month",
    "Last Month",
    "All Time",
  ];

  // Get employee details from mock data
  const employee = mockEmployeeDetails[id];

  // Reset to first page when tab, filter, or date range changes
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, filterOption, dateRange]);

  // Handle filter change
  const handleFilterChange = (option) => {
    setFilterOption(option);
    setDateRange({ startDate: null, endDate: null });
    // Apply filtering logic based on the selected option
    // This would filter the attendance, leaves, or project activities data
  };

  // Handle date range change
  const handleDateRangeChange = (startDate, endDate) => {
    setDateRange({ startDate, endDate });
    setFilterOption("Custom Range");

    // In a real app, you would filter based on the date range
    // For now, we'll just log it
    console.log("Date range:", startDate, endDate);
  };

  // Change page
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Change items per page
  const handleItemsPerPageChange = (newItemsPerPage) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  // Handle save edited employee
  const handleSaveEdit = (updatedEmployee) => {
    // In a real app, this would update the employee data in the backend
    // For now, we'll just log it
    console.log("Updated employee:", updatedEmployee);
  };

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Format datetime
  const formatDateTime = (dateTimeString) => {
    const date = new Date(dateTimeString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // If employee not found
  if (!employee) {
    return (
      <div className="glass-card p-8 rounded-lg text-center">
        <AlertCircle className="h-12 w-12 mx-auto text-accent-4 mb-4" />
        <h3 className="text-lg font-medium text-white mb-2">
          Employee Not Found
        </h3>
        <p className="text-text-secondary max-w-md mx-auto mb-6">
          The employee you are looking for does not exist or has been removed.
        </p>
        <button
          onClick={() => navigate("/dashboard/employees")}
          className="px-4 py-2 btn-white font-medium rounded-lg text-sm"
        >
          Back to Employees
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header with back button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate("/dashboard/employees")}
            className="p-2 mr-4 btn-white-ghost rounded-full"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div>
            <h2 className="text-2xl font-bold text-white">{employee.name}</h2>
            <p className="text-sm text-text-secondary">
              {employee.position} • {employee.department}
            </p>
          </div>
        </div>
        <button
          className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
          onClick={() => setShowEditModal(true)}
        >
          <Edit className="h-4 w-4 mr-2" />
          Edit Employee
        </button>
      </div>

      {/* Employee overview card */}
      <div className="glass-card rounded-lg overflow-hidden">
        <div className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Avatar and basic info */}
            <div className="flex flex-col items-center md:items-start">
              <div className="w-32 h-32 rounded-full bg-surface-3 mb-4 overflow-hidden">
                {employee.avatar ? (
                  <img
                    src={employee.avatar}
                    alt={employee.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.onerror = null;
                      e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(
                        employee.name
                      )}&background=2A2D3E&color=fff&size=128`;
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-surface-3 text-white text-4xl font-bold">
                    {employee.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </div>
                )}
              </div>
              <div
                className={`px-3 py-1 text-xs rounded-full mb-2 ${
                  employee.status === "Active"
                    ? "bg-green-900/30 text-green-300"
                    : "bg-red-900/30 text-red-300"
                }`}
              >
                {employee.status}
              </div>
              <div className="text-sm text-text-secondary mb-1">
                <span className="font-medium text-white">
                  {employee.employeeId}
                </span>
              </div>
              <div className="text-sm text-text-secondary">
                Joined {formatDate(employee.dateJoined)}
              </div>
            </div>

            {/* Employee details */}
            <div className="flex-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-md font-medium text-white mb-4">
                    Employee Information
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <Mail className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                      <div>
                        <p className="text-sm text-white">{employee.email}</p>
                        <p className="text-xs text-text-secondary">Email</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Briefcase className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                      <div>
                        <p className="text-sm text-white">
                          {employee.position}
                        </p>
                        <p className="text-xs text-text-secondary">Position</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Users className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                      <div>
                        <p className="text-sm text-white">
                          {employee.department}
                        </p>
                        <p className="text-xs text-text-secondary">
                          Department
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-md font-medium text-white mb-4">
                    Role & Management
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <Users className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                      <div>
                        <p className="text-sm text-white">{employee.manager}</p>
                        <p className="text-xs text-text-secondary">Manager</p>
                      </div>
                    </div>
                    <div className="flex items-start">
                      <Briefcase className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                      <div>
                        <p className="text-sm text-white">{employee.role}</p>
                        <p className="text-xs text-text-secondary">Role</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="text-md font-medium text-white mb-2">Bio</h3>
                <p className="text-sm text-text-secondary">{employee.bio}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-border">
        <div className="flex overflow-x-auto hide-scrollbar">
          <nav className="flex space-x-1" aria-label="Employee Tabs">
            <button
              onClick={() => setActiveTab("overview")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "overview"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Users className="h-4 w-4 inline mr-2" />
              Overview
            </button>

            <button
              onClick={() => setActiveTab("projects")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "projects"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Briefcase className="h-4 w-4 inline mr-2" />
              Project Activities
            </button>

            <button
              onClick={() => setActiveTab("leaves")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "leaves"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Calendar className="h-4 w-4 inline mr-2" />
              Leaves
            </button>

            <button
              onClick={() => setActiveTab("attendance")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "attendance"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Clock className="h-4 w-4 inline mr-2" />
              Attendance
            </button>
          </nav>
        </div>
      </div>

      {/* Tab content */}
      <div className="glass-card rounded-lg overflow-hidden">
        {/* Overview Tab */}
        {activeTab === "overview" && (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Bio section */}
              <div>
                <h3 className="text-md font-medium text-white mb-4">Bio</h3>
                <p className="text-sm text-text-secondary mb-6">
                  {employee.bio}
                </p>

                <h3 className="text-md font-medium text-white mb-4">
                  Work Information
                </h3>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <Briefcase className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm text-white">{employee.position}</p>
                      <p className="text-xs text-text-secondary">Position</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Users className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm text-white">
                        {employee.department}
                      </p>
                      <p className="text-xs text-text-secondary">Department</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-text-secondary mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm text-white">
                        {formatDate(employee.dateJoined)}
                      </p>
                      <p className="text-xs text-text-secondary">Date Joined</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats and recent activity */}
              <div>
                <h3 className="text-md font-medium text-white mb-4">
                  Quick Stats
                </h3>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <h4 className="text-xs text-text-secondary mb-1">
                      Activities
                    </h4>
                    <p className="text-xl font-bold text-white">
                      {mockTeamTasks.length}
                    </p>
                  </div>
                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <h4 className="text-xs text-text-secondary mb-1">
                      Leave Balance
                    </h4>
                    <p className="text-xl font-bold text-white">12 days</p>
                  </div>
                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <h4 className="text-xs text-text-secondary mb-1">
                      Attendance
                    </h4>
                    <p className="text-xl font-bold text-white">98%</p>
                  </div>
                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <h4 className="text-xs text-text-secondary mb-1">Tenure</h4>
                    <p className="text-xl font-bold text-white">1.5 years</p>
                  </div>
                </div>

                <h3 className="text-md font-medium text-white mb-4">
                  Recent Activity
                </h3>
                <div className="space-y-4">
                  {mockActivity.slice(0, 3).map((activity) => (
                    <div key={activity.id} className="flex items-start">
                      <div className="h-8 w-8 rounded-full bg-surface-3 flex items-center justify-center mr-3">
                        {activity.type === "check_in" && (
                          <Clock className="h-4 w-4 text-green-400" />
                        )}
                        {activity.type === "check_out" && (
                          <Clock className="h-4 w-4 text-blue-400" />
                        )}
                        {activity.type === "leave_request" && (
                          <Calendar className="h-4 w-4 text-purple-400" />
                        )}
                      </div>
                      <div>
                        <p className="text-sm text-white">
                          {activity.description}
                        </p>
                        <p className="text-xs text-text-secondary">
                          {formatDateTime(activity.date)}
                        </p>
                      </div>
                    </div>
                  ))}
                  <button
                    onClick={() => setActiveTab("attendance")}
                    className="text-sm text-accent-1 hover:text-accent-2"
                  >
                    View attendance
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Project Activities Tab */}
        {activeTab === "projects" && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-md font-medium text-white">
                Project Activities
              </h3>
              <div className="flex space-x-2">
                <FilterDropdown
                  options={filterOptions}
                  defaultOption="Today"
                  onFilterChange={handleFilterChange}
                  onDateRangeChange={handleDateRangeChange}
                  showDateRangePicker={true}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {mockTeamTasks.map((task) => (
                <div
                  key={task.id}
                  className="glass-card p-5 rounded-lg border border-border hover:border-accent-1 transition-colors"
                >
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="text-md font-medium text-white">
                      {task.title}
                    </h4>
                    <span
                      className={`px-2 py-1 text-xs rounded-full ${
                        task.status === "Completed"
                          ? "bg-green-900/30 text-green-300"
                          : task.status === "In Progress"
                          ? "bg-blue-900/30 text-blue-300"
                          : "bg-yellow-900/30 text-yellow-300"
                      }`}
                    >
                      {task.status}
                    </span>
                  </div>

                  <p className="text-sm text-text-secondary mb-4">
                    {task.description}
                  </p>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-xs text-text-secondary">Start Date</p>
                      <p className="text-sm text-white">
                        {formatDate(task.startDate)}
                      </p>
                    </div>
                    <div>
                      <p className="text-xs text-text-secondary">End Date</p>
                      <p className="text-sm text-white">
                        {formatDate(task.endDate)}
                      </p>
                    </div>
                  </div>

                  <div className="mb-2">
                    <p className="text-xs text-text-secondary mb-2">Progress</p>
                    <div className="w-full bg-surface h-2 rounded-full">
                      <div
                        className="bg-accent-1 h-2 rounded-full"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-text-secondary mt-1 text-right">
                      {task.progress}%
                    </p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8">
              <h3 className="text-md font-medium text-white mb-4">
                Recent Activities
              </h3>
              <div className="space-y-6">
                {mockActivity.map((activity) => (
                  <div key={activity.id} className="flex">
                    <div className="mr-4 relative">
                      <div className="h-10 w-10 rounded-full bg-surface-3 flex items-center justify-center z-10 relative">
                        {activity.type === "check_in" && (
                          <Clock className="h-5 w-5 text-green-400" />
                        )}
                        {activity.type === "check_out" && (
                          <Clock className="h-5 w-5 text-blue-400" />
                        )}
                        {activity.type === "leave_request" && (
                          <Calendar className="h-5 w-5 text-purple-400" />
                        )}
                      </div>
                      {/* Timeline connector */}
                      <div className="absolute top-10 bottom-0 left-1/2 w-0.5 -ml-px bg-border"></div>
                    </div>
                    <div className="glass-card p-4 rounded-lg flex-1 mb-6">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-sm font-medium text-white">
                          {activity.description}
                        </h4>
                        <span className="text-xs text-text-secondary">
                          {formatDateTime(activity.date)}
                        </span>
                      </div>
                      <div className="text-xs text-text-secondary">
                        {activity.type === "check_in" && activity.message && (
                          <p>Message: {activity.message}</p>
                        )}
                        {activity.type === "check_out" && activity.message && (
                          <p>Summary: {activity.message}</p>
                        )}
                        {activity.type === "leave_request" && (
                          <p>
                            Status:{" "}
                            <span
                              className={
                                activity.status === "Approved"
                                  ? "text-green-400"
                                  : "text-blue-400"
                              }
                            >
                              {activity.status}
                            </span>
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Leaves Tab */}
        {activeTab === "leaves" && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-md font-medium text-white">Leave History</h3>
              <div className="flex space-x-2">
                <FilterDropdown
                  options={filterOptions}
                  defaultOption="Today"
                  onFilterChange={handleFilterChange}
                  onDateRangeChange={handleDateRangeChange}
                  showDateRangePicker={true}
                />
              </div>
            </div>

            <div className="glass-card rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-surface-2 border-b border-border">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Type
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Requested On
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Approved By
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border">
                    {mockLeaves.map((leave) => (
                      <tr key={leave.id} className="hover:bg-surface-2">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-white">{leave.type}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-white">
                            {formatDate(leave.startDate)} -{" "}
                            {formatDate(leave.endDate)}
                          </div>
                          <div className="text-xs text-text-secondary">
                            {leave.duration}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              leave.status === "Approved"
                                ? "bg-green-900/30 text-green-300"
                                : leave.status === "Pending"
                                ? "bg-blue-900/30 text-blue-300"
                                : "bg-red-900/30 text-red-300"
                            }`}
                          >
                            {leave.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {formatDate(leave.requestedOn)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {leave.approvedBy || "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="p-4 border-t border-border">
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(leavesData.length / itemsPerPage)}
                  onPageChange={handlePageChange}
                  totalItems={leavesData.length}
                  itemsPerPage={itemsPerPage}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  showItemsPerPageSelect={true}
                  itemsPerPageOptions={[5, 10, 25, 50]}
                />
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-md font-medium text-white mb-4">
                Leave Balance
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="glass-card p-4 rounded-lg bg-surface-2">
                  <h4 className="text-xs text-text-secondary mb-1">
                    Annual Leave
                  </h4>
                  <p className="text-xl font-bold text-white">12 days</p>
                  <div className="w-full bg-surface h-1.5 rounded-full mt-2">
                    <div
                      className="bg-accent-1 h-1.5 rounded-full"
                      style={{ width: "60%" }}
                    ></div>
                  </div>
                  <p className="text-xs text-text-secondary mt-1">
                    12 of 20 days remaining
                  </p>
                </div>
                <div className="glass-card p-4 rounded-lg bg-surface-2">
                  <h4 className="text-xs text-text-secondary mb-1">
                    Sick Leave
                  </h4>
                  <p className="text-xl font-bold text-white">8 days</p>
                  <div className="w-full bg-surface h-1.5 rounded-full mt-2">
                    <div
                      className="bg-accent-1 h-1.5 rounded-full"
                      style={{ width: "80%" }}
                    ></div>
                  </div>
                  <p className="text-xs text-text-secondary mt-1">
                    8 of 10 days remaining
                  </p>
                </div>
                <div className="glass-card p-4 rounded-lg bg-surface-2">
                  <h4 className="text-xs text-text-secondary mb-1">
                    Personal Leave
                  </h4>
                  <p className="text-xl font-bold text-white">3 days</p>
                  <div className="w-full bg-surface h-1.5 rounded-full mt-2">
                    <div
                      className="bg-accent-1 h-1.5 rounded-full"
                      style={{ width: "50%" }}
                    ></div>
                  </div>
                  <p className="text-xs text-text-secondary mt-1">
                    3 of 6 days remaining
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Attendance Tab */}
        {activeTab === "attendance" && (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-md font-medium text-white">
                Attendance History
              </h3>
              <div className="flex space-x-2">
                <FilterDropdown
                  options={filterOptions}
                  defaultOption="Today"
                  onFilterChange={handleFilterChange}
                  onDateRangeChange={handleDateRangeChange}
                  showDateRangePicker={true}
                />
              </div>
            </div>

            <div className="glass-card rounded-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-surface-2 border-b border-border">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Check In
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Check Out
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Hours
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border">
                    {mockAttendance.map((record, index) => (
                      <tr key={index} className="hover:bg-surface-2">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {formatDate(record.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              record.status === "Present"
                                ? "bg-green-900/30 text-green-300"
                                : record.status === "On Leave"
                                ? "bg-blue-900/30 text-blue-300"
                                : "bg-gray-900/30 text-gray-300"
                            }`}
                          >
                            {record.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {record.checkIn || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {record.checkOut || "-"}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                          {record.checkIn && record.checkOut
                            ? (() => {
                                const checkIn = record.checkIn.split(":");
                                const checkOut = record.checkOut.split(":");
                                const hours =
                                  parseInt(checkOut[0]) -
                                  parseInt(checkIn[0]) +
                                  (parseInt(checkOut[1]) -
                                    parseInt(checkIn[1])) /
                                    60;
                                return hours.toFixed(1);
                              })()
                            : "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <div className="p-4 border-t border-border">
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(attendanceData.length / itemsPerPage)}
                  onPageChange={handlePageChange}
                  totalItems={attendanceData.length}
                  itemsPerPage={itemsPerPage}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  showItemsPerPageSelect={true}
                  itemsPerPageOptions={[5, 10, 25, 50]}
                />
              </div>
            </div>

            <div className="mt-8">
              <h3 className="text-md font-medium text-white mb-4">
                Today's Status
              </h3>
              <div className="glass-card p-5 rounded-lg">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="mb-4 md:mb-0">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-green-900/30 flex items-center justify-center mr-3">
                        <CheckCircle className="h-5 w-5 text-green-300" />
                      </div>
                      <div>
                        <h4 className="text-md font-medium text-white">
                          Checked In
                        </h4>
                        <p className="text-sm text-text-secondary">
                          Today at 9:02 AM
                        </p>
                      </div>
                    </div>
                  </div>

                  <button className="px-4 py-2 btn-white font-medium rounded-lg text-sm">
                    Check Out
                  </button>
                </div>

                <div className="mt-4 p-4 bg-surface-2 rounded-lg">
                  <h5 className="text-sm font-medium text-white mb-2">
                    Today's Work Summary
                  </h5>
                  <textarea
                    className="w-full bg-surface-3 border border-border rounded-lg p-3 text-sm text-white"
                    rows="3"
                    placeholder="What did you work on today?"
                  ></textarea>
                </div>
              </div>
            </div>

            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="glass-card p-5 rounded-lg">
                <h4 className="text-md font-medium text-white mb-4">
                  Monthly Summary
                </h4>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-text-secondary">
                        Present Days
                      </span>
                      <span className="text-sm text-white">18</span>
                    </div>
                    <div className="w-full bg-surface h-2 rounded-full">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: "85%" }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-text-secondary">
                        On Leave
                      </span>
                      <span className="text-sm text-white">2</span>
                    </div>
                    <div className="w-full bg-surface h-2 rounded-full">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: "10%" }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-text-secondary">
                        Weekends
                      </span>
                      <span className="text-sm text-white">8</span>
                    </div>
                    <div className="w-full bg-surface h-2 rounded-full">
                      <div
                        className="bg-gray-500 h-2 rounded-full"
                        style={{ width: "25%" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="glass-card p-5 rounded-lg">
                <h4 className="text-md font-medium text-white mb-4">
                  Working Hours
                </h4>
                <div className="flex items-center justify-center h-32">
                  <div className="text-center">
                    <p className="text-3xl font-bold text-white">152.5</p>
                    <p className="text-sm text-text-secondary">
                      Hours this month
                    </p>
                  </div>
                </div>
              </div>

              <div className="glass-card p-5 rounded-lg">
                <h4 className="text-md font-medium text-white mb-4">
                  Punctuality
                </h4>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-text-secondary">
                        On Time
                      </span>
                      <span className="text-sm text-white">15 days</span>
                    </div>
                    <div className="w-full bg-surface h-2 rounded-full">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: "75%" }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm text-text-secondary">Late</span>
                      <span className="text-sm text-white">3 days</span>
                    </div>
                    <div className="w-full bg-surface h-2 rounded-full">
                      <div
                        className="bg-yellow-500 h-2 rounded-full"
                        style={{ width: "15%" }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Edit Employee Modal */}
      <EditEmployeeModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        employee={employee}
        onSave={handleSaveEdit}
      />
    </div>
  );
};

export default EmployeeDetailsPage;
