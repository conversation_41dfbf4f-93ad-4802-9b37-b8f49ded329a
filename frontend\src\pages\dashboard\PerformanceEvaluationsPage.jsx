import {
  Star,
  Calendar,
  Clock,
  Filter,
  Plus,
  Users,
  CheckSquare,
  Briefcase,
  Search,
  ChevronDown,
  AlertCircle,
} from "lucide-react";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import CreateEvaluationModal from "../../components/dashboard/CreateEvaluationModal";

const PerformanceEvaluationsPage = () => {
  const navigate = useNavigate();

  // State for active tab
  const [activeTab, setActiveTab] = useState("completed");

  // State for modals
  const [showCreateModal, setShowCreateModal] = useState(false);

  // State for evaluations
  const [evaluations, setEvaluations] = useState([]);

  // State for upcoming evaluations
  const [upcomingEvaluations, setUpcomingEvaluations] = useState([]);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [filterPeriod, setFilterPeriod] = useState("all-time");
  const [showFilters, setShowFilters] = useState(false);

  // Mock data for performance evaluations
  const mockEvaluations = [
    {
      id: 1,
      activityId: 1,
      activityTitle: "Website Redesign",
      employeeName: "John Doe",
      rating: 4.5,
      feedback:
        "Excellent work on the design phase, delivered ahead of schedule.",
      date: "2023-11-15",
    },
    {
      id: 2,
      activityId: 3,
      activityTitle: "Client Onboarding Improvement",
      employeeName: "Jane Smith",
      rating: 5,
      feedback:
        "Outstanding leadership in redesigning the process. Resulted in 30% faster onboarding.",
      date: "2023-11-30",
    },
    {
      id: 3,
      activityId: 2,
      activityTitle: "Mobile App Development",
      employeeName: "Michael Johnson",
      rating: 3.5,
      feedback:
        "Good technical skills but needs improvement in communication and documentation.",
      date: "2023-12-05",
    },
    {
      id: 4,
      activityId: 4,
      activityTitle: "Database Migration",
      employeeName: "Sarah Williams",
      rating: 4.0,
      feedback:
        "Handled a complex migration with minimal downtime. Well executed.",
      date: "2023-12-10",
    },
  ];

  // Mock data for upcoming evaluations
  const mockUpcomingEvaluations = [
    {
      id: 101,
      employeeId: 1,
      employeeName: "John Doe",
      position: "Frontend Developer",
      dateJoined: "2023-01-15",
      lastEvaluationDate: "2023-09-15",
      nextEvaluationDate: "2023-12-15",
      daysUntilEvaluation: 5,
    },
    {
      id: 102,
      employeeId: 2,
      employeeName: "Jane Smith",
      position: "UX Designer",
      dateJoined: "2022-08-10",
      lastEvaluationDate: "2023-09-15",
      nextEvaluationDate: "2023-12-15",
      daysUntilEvaluation: 5,
    },
    {
      id: 103,
      employeeId: 3,
      employeeName: "Michael Johnson",
      position: "Backend Developer",
      dateJoined: "2023-03-22",
      lastEvaluationDate: "2023-09-15",
      nextEvaluationDate: "2023-12-20",
      daysUntilEvaluation: 10,
    },
    {
      id: 104,
      employeeId: 4,
      employeeName: "Sarah Williams",
      position: "Project Manager",
      dateJoined: "2021-11-05",
      lastEvaluationDate: "2023-09-15",
      nextEvaluationDate: "2023-12-25",
      daysUntilEvaluation: 15,
    },
  ];

  // Initialize evaluations from mock data
  useEffect(() => {
    // Transform mock data to include type and additional fields
    const transformedEvaluations = mockEvaluations.map((evaluation) => ({
      ...evaluation,
      type: "periodic", // Change to periodic for all evaluations
      rating: evaluation.rating * 2, // Convert 5-scale to 10-scale
      strengths:
        "Strong communication skills, technical expertise, and problem-solving abilities.",
      improvements:
        "Could improve documentation practices and time management.",
    }));

    setEvaluations(transformedEvaluations);
    setUpcomingEvaluations(mockUpcomingEvaluations);
  }, []);

  // Handle creating a new evaluation
  const handleCreateEvaluation = (newEvaluation) => {
    // Get employee and activity details
    const employee = newEvaluation.employeeId
      ? { name: `Employee ${newEvaluation.employeeId}` } // Replace with actual employee lookup
      : { name: "Unknown Employee" };

    const activity = newEvaluation.projectId
      ? { title: `Project ${newEvaluation.projectId}` } // Replace with actual project lookup
      : { title: "General Evaluation" };

    // Create formatted evaluation object
    const formattedEvaluation = {
      id: newEvaluation.id,
      type: newEvaluation.type,
      employeeName: employee.name,
      activityTitle: activity.title,
      rating: newEvaluation.rating || 0,
      feedback: newEvaluation.feedback || "",
      date: newEvaluation.date,
    };

    setEvaluations([formattedEvaluation, ...evaluations]);
  };

  // Handle opening evaluation details
  const handleEvaluationClick = (evaluation) => {
    // Navigate to the evaluation details page
    navigate(`/dashboard/evaluations/${evaluation.id}`);
  };

  // Filter evaluations based on search term and period
  const filteredEvaluations = evaluations.filter((evaluation) => {
    // Filter by search term
    if (
      searchTerm &&
      !evaluation.employeeName
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) &&
      !evaluation.activityTitle.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }

    // Filter by period
    if (filterPeriod !== "all-time") {
      const evalDate = new Date(evaluation.date);
      const now = new Date();

      switch (filterPeriod) {
        case "this-month":
          return (
            evalDate.getMonth() === now.getMonth() &&
            evalDate.getFullYear() === now.getFullYear()
          );
        case "this-quarter":
          const quarter = Math.floor(now.getMonth() / 3);
          const evalQuarter = Math.floor(evalDate.getMonth() / 3);
          return (
            evalQuarter === quarter &&
            evalDate.getFullYear() === now.getFullYear()
          );
        case "this-year":
          return evalDate.getFullYear() === now.getFullYear();
        default:
          return true;
      }
    }

    return true;
  });

  // Filter upcoming evaluations by search term
  const filteredUpcomingEvaluations = upcomingEvaluations.filter((employee) => {
    if (
      searchTerm &&
      !employee.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) &&
      !employee.position.toLowerCase().includes(searchTerm.toLowerCase())
    ) {
      return false;
    }
    return true;
  });

  // Function to render rating (1-10 scale)
  const renderRating = (rating) => {
    return (
      <div className="flex items-center">
        <div className="w-8 h-8 rounded-full bg-soft-blue text-white flex items-center justify-center">
          {rating}
        </div>
        <span className="ml-2 text-white">{rating}/10</span>
      </div>
    );
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">
            Performance Evaluations
          </h2>
          <p className="mt-1 text-sm text-text-secondary">
            Evaluate team members' performance using flexible assessment methods
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <button
            className="flex items-center px-4 py-2 text-white rounded-lg bg-surface-3 hover:bg-surface btn-white"
            onClick={() => setShowCreateModal(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Evaluation
          </button>
        </div>
      </div>

      {/* Evaluation Type Tabs */}
      <div className="flex flex-wrap border-b border-border">
        <button
          className={`px-4 py-2 text-sm font-medium border-b-2 ${
            activeTab === "upcoming"
              ? "border-accent-1 text-white"
              : "border-transparent text-text-secondary hover:text-white"
          }`}
          onClick={() => setActiveTab("upcoming")}
        >
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-1" />
            Upcoming Evaluations
          </div>
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium border-b-2 ${
            activeTab === "completed"
              ? "border-accent-1 text-white"
              : "border-transparent text-text-secondary hover:text-white"
          }`}
          onClick={() => setActiveTab("completed")}
        >
          <div className="flex items-center">
            <CheckSquare className="h-4 w-4 mr-1" />
            Completed Evaluations
          </div>
        </button>
        <button
          className={`px-4 py-2 text-sm font-medium border-b-2 ${
            activeTab === "projects"
              ? "border-accent-1 text-white"
              : "border-transparent text-text-secondary hover:text-white"
          }`}
          onClick={() => setActiveTab("projects")}
        >
          <div className="flex items-center">
            <Briefcase className="h-4 w-4 mr-1" />
            Project Reviews
          </div>
        </button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-grow">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-text-secondary" />
          </div>
          <input
            type="text"
            className="bg-surface-3 border border-border text-white text-sm rounded-lg focus:ring-accent-1 focus:border-accent-1 block w-full pl-10 p-2.5"
            placeholder="Search by employee or project..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="relative">
          <button
            className="px-4 py-2.5 bg-surface-3 text-white rounded-lg hover:bg-surface flex items-center"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Filter
            <ChevronDown className="h-4 w-4 ml-2" />
          </button>

          {showFilters && (
            <div className="absolute right-0 mt-2 w-48 bg-surface-3 rounded-lg shadow-lg z-10 border border-border">
              <div className="p-2">
                <h4 className="text-sm font-medium text-white mb-2">
                  Time Period
                </h4>
                <div className="space-y-1">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="all-time"
                      name="time-period"
                      value="all-time"
                      checked={filterPeriod === "all-time"}
                      onChange={() => setFilterPeriod("all-time")}
                      className="mr-2"
                    />
                    <label
                      htmlFor="all-time"
                      className="text-sm text-text-secondary"
                    >
                      All Time
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="this-month"
                      name="time-period"
                      value="this-month"
                      checked={filterPeriod === "this-month"}
                      onChange={() => setFilterPeriod("this-month")}
                      className="mr-2"
                    />
                    <label
                      htmlFor="this-month"
                      className="text-sm text-text-secondary"
                    >
                      This Month
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="this-quarter"
                      name="time-period"
                      value="this-quarter"
                      checked={filterPeriod === "this-quarter"}
                      onChange={() => setFilterPeriod("this-quarter")}
                      className="mr-2"
                    />
                    <label
                      htmlFor="this-quarter"
                      className="text-sm text-text-secondary"
                    >
                      This Quarter
                    </label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="this-year"
                      name="time-period"
                      value="this-year"
                      checked={filterPeriod === "this-year"}
                      onChange={() => setFilterPeriod("this-year")}
                      className="mr-2"
                    />
                    <label
                      htmlFor="this-year"
                      className="text-sm text-text-secondary"
                    >
                      This Year
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Content based on active tab */}
      {activeTab === "upcoming" && (
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">
              Upcoming Evaluations
            </h3>
            <div className="text-sm text-text-secondary">
              {filteredUpcomingEvaluations.length}{" "}
              {filteredUpcomingEvaluations.length === 1
                ? "employee"
                : "employees"}{" "}
              due for evaluation
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-surface-2">
                <tr>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Employee
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Position
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Date Joined
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Last Evaluation
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Next Evaluation
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {filteredUpcomingEvaluations.length > 0 ? (
                  filteredUpcomingEvaluations.map((employee) => (
                    <tr key={employee.id} className="hover:bg-surface-2">
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">
                          {employee.employeeName}
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="text-sm text-text-secondary">
                          {employee.position}
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="text-sm text-text-secondary">
                          {formatDate(employee.dateJoined)}
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="text-sm text-text-secondary">
                          {formatDate(employee.lastEvaluationDate)}
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-text-secondary mr-2" />
                          <span className="text-sm text-white">
                            {formatDate(employee.nextEvaluationDate)}
                          </span>
                          <span className="ml-2 text-xs px-2 py-0.5 bg-soft-blue bg-opacity-20 text-soft-blue rounded-full">
                            {employee.daysUntilEvaluation} days
                          </span>
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <button
                          onClick={() => {
                            // Pre-fill employee ID when creating evaluation
                            setShowCreateModal(true);
                          }}
                          className="px-3 py-1 text-xs bg-surface-3 text-white rounded-lg hover:bg-surface"
                        >
                          Start Evaluation
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="6"
                      className="px-5 py-8 text-center text-text-secondary"
                    >
                      No upcoming evaluations found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === "completed" && (
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">
              Completed Evaluations
            </h3>
            <div className="text-sm text-text-secondary">
              {filteredEvaluations.length}{" "}
              {filteredEvaluations.length === 1 ? "evaluation" : "evaluations"}
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-surface-2">
                <tr>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Employee
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Type
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Rating
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Date
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Feedback
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {filteredEvaluations.length > 0 ? (
                  filteredEvaluations.map((evaluation) => (
                    <tr
                      key={evaluation.id}
                      className="hover:bg-surface-2 cursor-pointer"
                      onClick={() => handleEvaluationClick(evaluation)}
                    >
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">
                          {evaluation.employeeName}
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-text-secondary mr-2" />
                          <div className="text-sm text-text-secondary">
                            {evaluation.type === "periodic"
                              ? "Periodic Review"
                              : "Project Review"}
                          </div>
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        {evaluation.rating ? (
                          renderRating(evaluation.rating)
                        ) : (
                          <span className="text-sm text-text-secondary">
                            N/A
                          </span>
                        )}
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-text-secondary mr-2" />
                          <span className="text-sm text-text-secondary">
                            {formatDate(evaluation.date)}
                          </span>
                        </div>
                      </td>
                      <td className="px-5 py-4">
                        <div className="text-sm text-text-secondary max-w-xs truncate">
                          {evaluation.feedback}
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="5"
                      className="px-5 py-8 text-center text-text-secondary"
                    >
                      No evaluations found. Create a new evaluation to get
                      started.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === "projects" && (
        <div className="glass-card rounded-lg overflow-hidden">
          <div className="px-5 py-4 bg-surface-2 border-b border-border flex justify-between items-center">
            <h3 className="text-lg font-medium text-white">Project Reviews</h3>
            <div className="text-sm text-text-secondary">
              No project reviews yet
            </div>
          </div>
          <div className="p-8 text-center text-text-secondary">
            <Briefcase className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="mb-4">No project reviews have been created yet.</p>
            <p className="text-sm">
              Project reviews can be created from the project details page when
              a project is completed.
            </p>
          </div>
        </div>
      )}

      {/* Create Evaluation Modal */}
      <CreateEvaluationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onCreateEvaluation={(evaluation) => {
          handleCreateEvaluation(evaluation);
          // Navigate to the evaluation details page after creation
          navigate(`/dashboard/evaluations/${evaluation.id}`);
        }}
        evaluationType="periodic"
      />
    </div>
  );
};

export default PerformanceEvaluationsPage;
