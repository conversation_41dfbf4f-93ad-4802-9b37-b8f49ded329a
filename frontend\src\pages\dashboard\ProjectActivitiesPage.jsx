import { useState, useEffect } from "react";
import { LayoutGrid, List, ChevronRight, Search, X, Plus } from "lucide-react";
// Import project components
import KanbanBoard from "../../components/project/KanbanBoard";
import ProjectsTable from "../../components/project/ProjectsTable";
import CreateProjectModal from "../../components/dashboard/CreateProjectModal";
import CreateTaskModal from "../../components/dashboard/CreateTaskModal";
import TaskDetailsModal from "../../components/project/TaskDetailsModal";

const ProjectActivitiesPage = () => {
  // State for projects and tasks
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [viewMode, setViewMode] = useState("kanban"); // kanban or list
  const [showProjectModal, setShowProjectModal] = useState(false);
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showTaskDetailsModal, setShowTaskDetailsModal] = useState(false);
  const [currentColumnId, setCurrentColumnId] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");

  // Mock team members for task assignment
  const teamMembers = [
    { id: 1, name: "John Doe", role: "Developer" },
    { id: 2, name: "Jane Smith", role: "Designer" },
    { id: 3, name: "Mike Johnson", role: "Project Manager" },
    { id: 4, name: "Sarah Williams", role: "Marketing" },
  ];

  // Initialize with mock data
  useEffect(() => {
    // Mock data for project activities
    const mockProjects = [
      {
        id: 1,
        title: "Website Redesign",
        description:
          "Complete overhaul of the company website with new branding",
        createdAt: "2023-11-01T00:00:00.000Z",
        dueDate: "2023-12-15",
        team: [1, 2, 3],
        viewType: "kanban",
        columns: [
          { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
          { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
          { id: "completed", title: "Completed", color: "bg-green-500" },
        ],
        tasks: [
          {
            id: "101",
            title: "Design Phase",
            description: "Create wireframes and mockups for the new website",
            status: "Completed",
            assignees: [2],
            completion: 100,
            dueDate: "2023-11-15",
            projectId: 1,
            checklist: [
              { id: 1, text: "Create wireframes", completed: true },
              { id: 2, text: "Design mockups", completed: true },
              { id: 3, text: "Get client approval", completed: true },
            ],
            labels: [
              { id: 1, name: "Design", color: "bg-purple-500 text-white" },
            ],
          },
          {
            id: "102",
            title: "Development",
            description:
              "Implement the approved designs into a working website",
            status: "In Progress",
            assignees: [1, 3],
            completion: 65,
            dueDate: "2023-12-01",
            projectId: 1,
            checklist: [
              { id: 1, text: "Setup project structure", completed: true },
              { id: 2, text: "Implement homepage", completed: true },
              { id: 3, text: "Implement about page", completed: false },
              { id: 4, text: "Implement contact form", completed: false },
            ],
            labels: [
              { id: 1, name: "Frontend", color: "bg-blue-500 text-white" },
            ],
          },
          {
            id: "103",
            title: "Testing",
            description:
              "Test the website for bugs and ensure it works across all devices",
            status: "Not Started",
            assignees: [1, 4],
            completion: 0,
            dueDate: "2023-12-10",
            projectId: 1,
            checklist: [
              { id: 1, text: "Cross-browser testing", completed: false },
              { id: 2, text: "Mobile responsiveness", completed: false },
              { id: 3, text: "Performance testing", completed: false },
            ],
            labels: [{ id: 1, name: "QA", color: "bg-yellow-500 text-white" }],
          },
        ],
      },
      {
        id: 2,
        title: "Q4 Marketing Campaign",
        description: "Plan and execute Q4 marketing initiatives",
        createdAt: "2023-11-10T00:00:00.000Z",
        dueDate: "2023-12-30",
        team: [3, 4],
        viewType: "list",
        columns: [
          { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
          { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
          { id: "completed", title: "Completed", color: "bg-green-500" },
        ],
        tasks: [
          {
            id: "201",
            title: "Strategy Planning",
            description: "Define marketing goals and strategies for Q4",
            status: "Not Started",
            assignees: [3, 4],
            completion: 0,
            dueDate: "2023-11-20",
            projectId: 2,
            checklist: [],
          },
          {
            id: "202",
            title: "Content Creation",
            description: "Create content for social media, email, and blog",
            status: "Not Started",
            assignees: [4],
            completion: 0,
            dueDate: "2023-12-10",
            projectId: 2,
            checklist: [],
          },
          {
            id: "203",
            title: "Campaign Launch",
            description: "Launch and monitor the marketing campaign",
            status: "Not Started",
            assignees: [3, 4],
            completion: 0,
            dueDate: "2023-12-15",
            projectId: 2,
            checklist: [],
          },
        ],
      },
    ];

    setProjects(mockProjects);
    if (mockProjects.length > 0) {
      setSelectedProject(mockProjects[0]);
    }
  }, []);

  // Function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-500";
      case "In Progress":
        return "bg-blue-500";
      case "Not Started":
        return "bg-yellow-500";
      default:
        return "bg-gray-500";
    }
  };

  // Handle project creation
  const handleCreateProject = (newProject) => {
    setProjects([...projects, newProject]);
    setSelectedProject(newProject);
  };

  // Handle task creation
  const handleCreateTask = (newTask) => {
    const updatedProjects = projects.map((project) => {
      if (project.id === selectedProject.id) {
        return {
          ...project,
          tasks: [...project.tasks, newTask],
        };
      }
      return project;
    });

    setProjects(updatedProjects);
    setSelectedProject(
      updatedProjects.find((p) => p.id === selectedProject.id)
    );
  };

  // Handle task update
  const handleUpdateTask = (updatedTask) => {
    const updatedProjects = projects.map((project) => {
      if (project.id === selectedProject.id) {
        return {
          ...project,
          tasks: project.tasks.map((task) =>
            task.id === updatedTask.id ? updatedTask : task
          ),
        };
      }
      return project;
    });

    setProjects(updatedProjects);
    setSelectedProject(
      updatedProjects.find((p) => p.id === selectedProject.id)
    );
    setSelectedTask(updatedTask);
  };

  // Handle task deletion
  const handleDeleteTask = (taskId) => {
    const updatedProjects = projects.map((project) => {
      if (project.id === selectedProject.id) {
        return {
          ...project,
          tasks: project.tasks.filter((task) => task.id !== taskId),
        };
      }
      return project;
    });

    setProjects(updatedProjects);
    setSelectedProject(
      updatedProjects.find((p) => p.id === selectedProject.id)
    );
    setSelectedTask(null);
    setShowTaskDetailsModal(false);
  };

  // Handle task move in Kanban board
  const handleTaskMove = ({ taskId, destinationColumnId }) => {
    // Find the task
    const task = selectedProject.tasks.find((t) => t.id === taskId);
    if (!task) return;

    // Update task status based on destination column
    const newStatus = destinationColumnId
      .replace(/-/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());

    const updatedTask = { ...task, status: newStatus };

    // Update the project
    handleUpdateTask(updatedTask);
  };

  // Open task creation modal
  const handleAddTask = (columnId) => {
    setCurrentColumnId(columnId);
    setShowTaskModal(true);
  };

  // Open task details modal
  const handleTaskClick = (task) => {
    setSelectedTask(task);
    setShowTaskDetailsModal(true);
  };

  // Filter tasks by search query
  const filteredTasks =
    selectedProject?.tasks.filter(
      (task) =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  return (
    <div className="space-y-6">
      {!selectedProject && (
        <ProjectsTable
          projects={projects}
          onNewProject={() => setShowProjectModal(true)}
        />
      )}

      {/* Project detail view - Only visible when a project is selected */}
      {selectedProject && (
        <div className="overflow-hidden rounded-lg glass-card">
          <div className="flex justify-between items-center px-5 py-4 border-b bg-surface-2 border-border">
            <div className="flex items-center">
              <h3 className="text-lg font-medium text-white">
                {selectedProject.title}
              </h3>
              <ChevronRight className="mx-1 w-5 h-5 text-text-secondary" />
              <span className="text-sm text-text-secondary">
                {selectedProject.tasks.length} tasks
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                  <Search className="w-4 h-4 text-text-secondary" />
                </div>
                <input
                  type="text"
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block p-2 pl-10 w-full text-sm text-white rounded-lg border bg-surface-3 border-border focus:ring-accent-1 focus:border-accent-1"
                />
              </div>
              <button
                className={`p-2 text-sm rounded-lg ${
                  viewMode === "list"
                    ? "bg-surface text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => setViewMode("list")}
                title="List View"
              >
                <List className="w-4 h-4" />
              </button>
              <button
                className={`p-2 text-sm rounded-lg ${
                  viewMode === "kanban"
                    ? "bg-surface text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => setViewMode("kanban")}
                title="Kanban View"
              >
                <LayoutGrid className="w-4 h-4" />
              </button>
              <button
                className="p-2 text-sm text-white rounded-lg bg-surface-3 hover:bg-surface"
                onClick={() => handleAddTask("not-started")}
                title="Add Task"
              >
                <Plus className="w-4 h-4" />
              </button>
              <button
                className="p-2 text-sm text-white rounded-lg bg-surface-3 hover:bg-surface"
                onClick={() => setSelectedProject(null)}
                title="Close Project"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* View based on selected mode */}
          {viewMode === "kanban" ? (
            <div className="p-4 h-[calc(100vh-20rem)]">
              <KanbanBoard
                project={selectedProject}
                onTaskClick={handleTaskClick}
                onAddTask={handleAddTask}
                onTaskMove={handleTaskMove}
              />
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-surface-2">
                  <tr>
                    <th
                      scope="col"
                      className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                    >
                      Task
                    </th>
                    <th
                      scope="col"
                      className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                    >
                      Assignees
                    </th>
                    <th
                      scope="col"
                      className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                    >
                      Progress
                    </th>
                    <th
                      scope="col"
                      className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                    >
                      Due Date
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {filteredTasks.map((task) => (
                    <tr
                      key={task.id}
                      className="cursor-pointer hover:bg-surface-2"
                      onClick={() => handleTaskClick(task)}
                    >
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-white">
                          {task.title}
                        </div>
                        <div className="mt-1 text-xs text-text-secondary">
                          {task.description}
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            task.status
                          )} bg-opacity-10 text-white`}
                        >
                          {task.status}
                        </span>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Users className="mr-2 w-4 h-4 text-text-secondary" />
                          <span className="text-sm text-text-secondary">
                            {task.assignees ? task.assignees.length : 0}
                          </span>
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full bg-surface-3 rounded-full h-2.5">
                            <div
                              className="bg-soft-blue h-2.5 rounded-full"
                              style={{ width: `${task.completion}%` }}
                            ></div>
                          </div>
                          <span className="ml-2 text-xs text-text-secondary">
                            {task.completion}%
                          </span>
                        </div>
                      </td>
                      <td className="px-5 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Calendar className="mr-2 w-4 h-4 text-text-secondary" />
                          <span className="text-sm text-text-secondary">
                            {task.dueDate}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {filteredTasks.length === 0 && (
                <div className="py-8 text-center">
                  <p className="text-text-secondary">
                    No tasks found. Create a new task or adjust your search.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Modals */}
      <CreateProjectModal
        isOpen={showProjectModal}
        onClose={() => setShowProjectModal(false)}
        onCreateProject={handleCreateProject}
      />

      <CreateTaskModal
        isOpen={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        onCreateTask={handleCreateTask}
        projectId={selectedProject?.id}
        columnId={currentColumnId}
        teamMembers={teamMembers}
      />

      <TaskDetailsModal
        isOpen={showTaskDetailsModal}
        onClose={() => setShowTaskDetailsModal(false)}
        task={selectedTask}
        teamMembers={teamMembers}
        onUpdateTask={handleUpdateTask}
        onDeleteTask={handleDeleteTask}
      />
    </div>
  );
};

export default ProjectActivitiesPage;
