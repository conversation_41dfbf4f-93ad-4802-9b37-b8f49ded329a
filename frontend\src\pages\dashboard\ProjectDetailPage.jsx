import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  LayoutGrid,
  List,
  ChevronRight,
  Search,
  X,
  Plus,
  Milestone,
  Star,
  CheckCircle,
} from "lucide-react";
// Import project components
import KanbanBoard from "../../components/project/KanbanBoard";
import CreateTaskModal from "../../components/dashboard/CreateTaskModal";
import TaskDetailsModal from "../../components/project/TaskDetailsModal";
import MilestonesView from "../../components/project/MilestonesView";
import ProjectReviewModal from "../../components/project/ProjectReviewModal";

const ProjectDetailPage = () => {
  const { projectId } = useParams();
  const navigate = useNavigate();

  // State for project and tasks
  const [project, setProject] = useState(null);
  const [selectedTask, setSelectedTask] = useState(null);
  const [viewMode, setViewMode] = useState("kanban"); // kanban, list, or milestones
  const [showTaskModal, setShowTaskModal] = useState(false);
  const [showTaskDetailsModal, setShowTaskDetailsModal] = useState(false);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [currentColumnId, setCurrentColumnId] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [projectCompleted, setProjectCompleted] = useState(false);

  // Mock team members for task assignment
  const teamMembers = [
    { id: 1, name: "John Doe", role: "Developer" },
    { id: 2, name: "Jane Smith", role: "Designer" },
    { id: 3, name: "Mike Johnson", role: "Project Manager" },
    { id: 4, name: "Sarah Williams", role: "Marketing" },
  ];

  // Handle project review creation
  const handleCreateReview = (review) => {
    console.log("Project review created:", review);
    // In a real app, you would save this to your backend
    // For now, we'll just close the modal
    setShowReviewModal(false);

    // Navigate to performance evaluations page to see the review
    navigate("/dashboard/performance-evaluations");
  };

  // Check if project is completed
  useEffect(() => {
    if (project) {
      const allTasks = project.tasks || [];
      const completedTasks = allTasks.filter(
        (task) => task.status === "Completed"
      );

      // If all tasks are completed, mark project as completed
      setProjectCompleted(
        allTasks.length > 0 && completedTasks.length === allTasks.length
      );
    }
  }, [project]);

  // Fetch project data
  useEffect(() => {
    // Mock data for project activities - in a real app, this would be an API call
    const mockProjects = [
      {
        id: 1,
        title: "Website Redesign",
        description:
          "Complete overhaul of the company website with new branding",
        createdAt: "2023-11-01T00:00:00.000Z",
        dueDate: "2023-12-15",
        team: [1, 2, 3],
        viewType: "kanban",
        completed: false,
        columns: [
          { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
          { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
          { id: "completed", title: "Completed", color: "bg-green-500" },
        ],
        tasks: [
          {
            id: "101",
            title: "Design Homepage",
            description: "Create new homepage design with updated branding",
            status: "Completed",
            assignees: [2],
            completion: 100,
            dueDate: "2023-11-15",
            projectId: 1,
            checklist: [
              { id: 1, text: "Wireframes", completed: true },
              { id: 2, text: "Visual design", completed: true },
              { id: 3, text: "Responsive layouts", completed: true },
            ],
            labels: [
              { id: 1, name: "Design", color: "bg-purple-500 text-white" },
            ],
          },
          {
            id: "102",
            title: "Develop Frontend",
            description: "Implement the new design with React components",
            status: "In Progress",
            assignees: [1],
            completion: 60,
            dueDate: "2023-12-01",
            projectId: 1,
            checklist: [
              { id: 1, text: "Setup project", completed: true },
              { id: 2, text: "Implement components", completed: true },
              { id: 3, text: "Connect to API", completed: false },
              { id: 4, text: "Testing", completed: false },
            ],
            labels: [
              { id: 1, name: "Development", color: "bg-blue-500 text-white" },
            ],
          },
          {
            id: "103",
            title: "Testing",
            description:
              "Test the website for bugs and ensure it works across all devices",
            status: "Not Started",
            assignees: [1, 4],
            completion: 0,
            dueDate: "2023-12-10",
            projectId: 1,
            checklist: [
              { id: 1, text: "Cross-browser testing", completed: false },
              { id: 2, text: "Mobile responsiveness", completed: false },
              { id: 3, text: "Performance testing", completed: false },
            ],
            labels: [{ id: 1, name: "QA", color: "bg-yellow-500 text-white" }],
          },
        ],
      },
      {
        id: 2,
        title: "Q4 Marketing Campaign",
        description: "Plan and execute Q4 marketing initiatives",
        createdAt: "2023-11-10T00:00:00.000Z",
        dueDate: "2023-12-30",
        team: [3, 4],
        viewType: "list",
        columns: [
          { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
          { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
          { id: "completed", title: "Completed", color: "bg-green-500" },
        ],
        tasks: [
          {
            id: "201",
            title: "Social Media Strategy",
            description: "Develop social media content calendar for Q4",
            status: "In Progress",
            assignees: [4],
            completion: 50,
            dueDate: "2023-11-20",
            projectId: 2,
            checklist: [
              { id: 1, text: "Research trends", completed: true },
              { id: 2, text: "Create content calendar", completed: true },
              { id: 3, text: "Design templates", completed: false },
              { id: 4, text: "Schedule posts", completed: false },
            ],
            labels: [
              { id: 1, name: "Marketing", color: "bg-red-500 text-white" },
            ],
          },
        ],
      },
    ];

    // Find the project by ID
    const foundProject = mockProjects.find((p) => p.id === parseInt(projectId));
    if (foundProject) {
      setProject(foundProject);
      // Set view mode based on project preference
      if (foundProject.viewType) {
        setViewMode(foundProject.viewType);
      }
    } else {
      // Project not found, redirect to projects list
      navigate("/dashboard/projects");
    }
  }, [projectId, navigate]);

  // Function to get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "Completed":
        return "bg-green-500";
      case "In Progress":
        return "bg-blue-500";
      case "Not Started":
        return "bg-yellow-500";
      default:
        return "bg-gray-500";
    }
  };

  // Handle task creation
  const handleCreateTask = (newTask) => {
    if (!project) return;

    const updatedProject = {
      ...project,
      tasks: [...project.tasks, newTask],
    };

    setProject(updatedProject);
  };

  // Handle task update
  const handleUpdateTask = (updatedTask) => {
    if (!project) return;

    const updatedTasks = project.tasks.map((task) =>
      task.id === updatedTask.id ? updatedTask : task
    );

    setProject({
      ...project,
      tasks: updatedTasks,
    });
  };

  // Handle project update (for milestones, etc.)
  const handleUpdateProject = (updatedProject) => {
    setProject(updatedProject);
  };

  // Handle task deletion
  const handleDeleteTask = (taskId) => {
    if (!project) return;

    const updatedTasks = project.tasks.filter((task) => task.id !== taskId);

    setProject({
      ...project,
      tasks: updatedTasks,
    });
  };

  // Handle task move in Kanban board
  const handleTaskMove = ({ taskId, destinationColumnId }) => {
    if (!project) return;

    // Find the task
    const task = project.tasks.find((t) => t.id === taskId);
    if (!task) return;

    // Update task status based on destination column
    const newStatus = destinationColumnId
      .replace(/-/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase());

    const updatedTask = { ...task, status: newStatus };

    // Update the project
    handleUpdateTask(updatedTask);
  };

  // Open task creation modal
  const handleAddTask = (columnId) => {
    setCurrentColumnId(columnId);
    setShowTaskModal(true);
  };

  // Open task details modal
  const handleTaskClick = (task) => {
    setSelectedTask(task);
    setShowTaskDetailsModal(true);
  };

  // Filter tasks by search query
  const filteredTasks =
    project?.tasks.filter(
      (task) =>
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  // If project is still loading
  if (!project) {
    return <div className="p-4 text-white">Loading project...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Project detail view */}
      <div className="overflow-hidden rounded-lg glass-card">
        <div className="flex justify-between items-center px-5 py-4 border-b bg-surface-2 border-border">
          <div className="flex items-center">
            <h3 className="text-lg font-medium text-white">{project.title}</h3>
            <ChevronRight className="mx-1 w-5 h-5 text-text-secondary" />
            <span className="text-sm text-text-secondary">
              {project.tasks.length} tasks
            </span>
            {projectCompleted && (
              <span className="ml-2 px-2 py-0.5 text-xs bg-green-500 bg-opacity-20 text-green-400 rounded-full flex items-center">
                <CheckCircle className="w-3 h-3 mr-1" />
                Completed
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-text-secondary" />
              </div>
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block p-2 pl-10 w-full text-sm text-white rounded-lg border bg-surface-3 border-border focus:ring-accent-1 focus:border-accent-1"
              />
            </div>
            <button
              className={`p-2 text-sm rounded-lg ${
                viewMode === "list"
                  ? "bg-surface text-white"
                  : "bg-surface-3 text-text-secondary hover:text-white"
              }`}
              onClick={() => setViewMode("list")}
              title="List View"
            >
              <List className="w-4 h-4" />
            </button>
            <button
              className={`p-2 text-sm rounded-lg ${
                viewMode === "kanban"
                  ? "bg-surface text-white"
                  : "bg-surface-3 text-text-secondary hover:text-white"
              }`}
              onClick={() => setViewMode("kanban")}
              title="Kanban View"
            >
              <LayoutGrid className="w-4 h-4" />
            </button>
            {project.projectFormat === "milestones" && (
              <button
                className={`p-2 text-sm rounded-lg ${
                  viewMode === "milestones"
                    ? "bg-surface text-white"
                    : "bg-surface-3 text-text-secondary hover:text-white"
                }`}
                onClick={() => setViewMode("milestones")}
                title="Milestones View"
              >
                <Milestone className="w-4 h-4" />
              </button>
            )}
            <button
              className="p-2 text-sm text-white rounded-lg bg-surface-3 hover:bg-surface"
              onClick={() => handleAddTask("not-started")}
              title="Add Task"
            >
              <Plus className="w-4 h-4" />
            </button>
            {projectCompleted && (
              <button
                className="p-2 text-sm text-white rounded-lg bg-soft-blue hover:bg-soft-blue/80 flex items-center"
                onClick={() => setShowReviewModal(true)}
                title="Review Project"
              >
                <Star className="w-4 h-4 mr-1" />
                <span className="hidden sm:inline">Review</span>
              </button>
            )}
            <button
              className="p-2 text-sm text-white rounded-lg bg-surface-3 hover:bg-surface"
              onClick={() => navigate("/dashboard/projects")}
              title="Back to Projects"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* View based on selected mode */}
        {viewMode === "kanban" ? (
          <div className="p-4 h-[calc(100vh-20rem)]">
            <KanbanBoard
              project={project}
              onTaskClick={handleTaskClick}
              onAddTask={handleAddTask}
              onTaskMove={handleTaskMove}
            />
          </div>
        ) : viewMode === "milestones" ? (
          <div className="p-4">
            <MilestonesView
              project={project}
              onUpdateProject={handleUpdateProject}
            />
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-surface-2">
                <tr>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Task
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Status
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Assignees
                  </th>
                  <th
                    scope="col"
                    className="px-5 py-3.5 text-left text-xs font-medium text-text-secondary uppercase tracking-wider"
                  >
                    Due Date
                  </th>
                </tr>
              </thead>
              <tbody className="bg-surface-3 divide-y divide-border">
                {filteredTasks.map((task) => (
                  <tr
                    key={task.id}
                    className="hover:bg-surface-2 cursor-pointer"
                    onClick={() => handleTaskClick(task)}
                  >
                    <td className="px-5 py-4">
                      <div className="flex items-center">
                        <div className="text-sm font-medium text-white">
                          {task.title}
                        </div>
                      </div>
                    </td>
                    <td className="px-5 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                          task.status
                        )} bg-opacity-10 text-white`}
                      >
                        {task.status}
                      </span>
                    </td>
                    <td className="px-5 py-4 whitespace-nowrap">
                      <div className="flex -space-x-1">
                        {task.assignees?.map((assigneeId) => {
                          const member = teamMembers.find(
                            (m) => m.id === assigneeId
                          );
                          return (
                            <div
                              key={assigneeId}
                              className="w-6 h-6 rounded-full bg-surface flex items-center justify-center text-xs text-white"
                              title={member?.name || "Unknown"}
                            >
                              {member?.name.charAt(0) || "?"}
                            </div>
                          );
                        })}
                      </div>
                    </td>
                    <td className="px-5 py-4 whitespace-nowrap">
                      <span className="text-sm text-text-secondary">
                        {task.dueDate}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {filteredTasks.length === 0 && (
              <div className="py-8 text-center">
                <p className="text-text-secondary">
                  No tasks found. Create a new task or adjust your search.
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Modals */}
      <CreateTaskModal
        isOpen={showTaskModal}
        onClose={() => setShowTaskModal(false)}
        onCreateTask={handleCreateTask}
        projectId={project?.id}
        columnId={currentColumnId}
        teamMembers={teamMembers}
      />

      <TaskDetailsModal
        isOpen={showTaskDetailsModal}
        onClose={() => setShowTaskDetailsModal(false)}
        task={selectedTask}
        teamMembers={teamMembers}
        onUpdateTask={handleUpdateTask}
        onDeleteTask={handleDeleteTask}
      />

      {/* Project Review Modal */}
      <ProjectReviewModal
        isOpen={showReviewModal}
        onClose={() => setShowReviewModal(false)}
        projectId={project?.id}
        onCreateReview={handleCreateReview}
      />
    </div>
  );
};

export default ProjectDetailPage;
