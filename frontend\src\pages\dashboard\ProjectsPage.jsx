import { useState, useEffect } from "react";
import ProjectsTable from "../../components/project/ProjectsTable";
import CreateProjectModal from "../../components/project/CreateProjectModal";

const ProjectsPage = () => {
  // State for projects
  const [projects, setProjects] = useState([]);
  const [showProjectModal, setShowProjectModal] = useState(false);

  // Initialize with mock data
  useEffect(() => {
    // Mock data for projects
    const mockProjects = [
      {
        id: 1,
        title: "Website Redesign",
        description:
          "Complete overhaul of the company website with new branding",
        createdAt: "2023-11-01T00:00:00.000Z",
        dueDate: "2023-12-15",
        team: [1, 2, 3],
        viewType: "kanban",
        columns: [
          { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
          { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
          { id: "completed", title: "Completed", color: "bg-green-500" },
        ],
        tasks: [
          {
            id: "101",
            title: "Design Homepage",
            description: "Create new homepage design with updated branding",
            status: "Completed",
            assignees: [2],
            completion: 100,
            dueDate: "2023-11-15",
            projectId: 1,
            checklist: [
              { id: 1, text: "Wireframes", completed: true },
              { id: 2, text: "Visual design", completed: true },
              { id: 3, text: "Responsive layouts", completed: true },
            ],
            labels: [{ id: 1, name: "Design", color: "bg-purple-500 text-white" }],
          },
          {
            id: "102",
            title: "Develop Frontend",
            description: "Implement the new design with React components",
            status: "In Progress",
            assignees: [1],
            completion: 60,
            dueDate: "2023-12-01",
            projectId: 1,
            checklist: [
              { id: 1, text: "Setup project", completed: true },
              { id: 2, text: "Implement components", completed: true },
              { id: 3, text: "Connect to API", completed: false },
              { id: 4, text: "Testing", completed: false },
            ],
            labels: [{ id: 1, name: "Development", color: "bg-blue-500 text-white" }],
          },
          {
            id: "103",
            title: "Testing",
            description:
              "Test the website for bugs and ensure it works across all devices",
            status: "Not Started",
            assignees: [1, 4],
            completion: 0,
            dueDate: "2023-12-10",
            projectId: 1,
            checklist: [
              { id: 1, text: "Cross-browser testing", completed: false },
              { id: 2, text: "Mobile responsiveness", completed: false },
              { id: 3, text: "Performance testing", completed: false },
            ],
            labels: [{ id: 1, name: "QA", color: "bg-yellow-500 text-white" }],
          },
        ],
      },
      {
        id: 2,
        title: "Q4 Marketing Campaign",
        description: "Plan and execute Q4 marketing initiatives",
        createdAt: "2023-11-10T00:00:00.000Z",
        dueDate: "2023-12-30",
        team: [3, 4],
        viewType: "list",
        columns: [
          { id: "not-started", title: "Not Started", color: "bg-yellow-500" },
          { id: "in-progress", title: "In Progress", color: "bg-blue-500" },
          { id: "completed", title: "Completed", color: "bg-green-500" },
        ],
        tasks: [
          {
            id: "201",
            title: "Social Media Strategy",
            description: "Develop social media content calendar for Q4",
            status: "In Progress",
            assignees: [4],
            completion: 50,
            dueDate: "2023-11-20",
            projectId: 2,
            checklist: [
              { id: 1, text: "Research trends", completed: true },
              { id: 2, text: "Create content calendar", completed: true },
              { id: 3, text: "Design templates", completed: false },
              { id: 4, text: "Schedule posts", completed: false },
            ],
            labels: [{ id: 1, name: "Marketing", color: "bg-red-500 text-white" }],
          },
        ],
      },
    ];

    setProjects(mockProjects);
  }, []);

  // Handle project creation
  const handleCreateProject = (newProject) => {
    setProjects([...projects, newProject]);
  };

  return (
    <div className="space-y-6">
      <ProjectsTable
        projects={projects}
        onNewProject={() => setShowProjectModal(true)}
      />

      {/* Modals */}
      <CreateProjectModal
        isOpen={showProjectModal}
        onClose={() => setShowProjectModal(false)}
        onCreateProject={handleCreateProject}
      />
    </div>
  );
};

export default ProjectsPage;
