import { useState, useEffect } from "react";
import {
  Settings,
  Save,
  Building,
  User,
  Clock,
  Calendar,
  CheckSquare,
  Bell,
  Globe,
  Mail,
  Phone,
  Shield,
  Users,
  FileText,
  BarChart,
  X,
  MessageSquare,
  Briefcase,
  List,
  Link2,
  CreditCard,
  ExternalLink,
  AlertCircle,
  Download,
  DollarSign,
  CheckCircle,
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";

const SettingsPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(getTabFromUrl() || "general");

  // Get active tab from URL
  function getTabFromUrl() {
    const path = location.pathname;
    if (path.includes("/settings/company")) return "company";
    if (path.includes("/settings/profile")) return "profile";
    if (path.includes("/settings/attendance")) return "attendance";
    if (path.includes("/settings/leave")) return "leave";
    if (path.includes("/settings/activities")) return "activities";
    if (path.includes("/settings/notifications")) return "notifications";
    if (path.includes("/settings/integrations")) return "integrations";
    if (path.includes("/settings/billing")) return "billing";
    if (path.includes("/settings/projects")) return "activities"; // Map projects to activities tab
    return "general";
  }

  // Update activeTab when URL changes
  useEffect(() => {
    const tabFromUrl = getTabFromUrl();
    if (tabFromUrl !== activeTab) {
      setActiveTab(tabFromUrl);
    }
  }, [location.pathname]);

  // Change tab and update URL
  const changeTab = (tab) => {
    setActiveTab(tab);
    if (tab === "general") {
      navigate("/dashboard/settings");
    } else {
      navigate(`/dashboard/settings/${tab}`);
    }
  };

  // Settings state
  const [generalSettings, setGeneralSettings] = useState({
    companyName: "TeamCheck Inc.",
    timezone: "UTC",
    language: "en",
    dateFormat: "MM/DD/YYYY",
    timeFormat: "12h",
  });

  const [companySettings, setCompanySettings] = useState({
    companyName: "TeamCheck Inc.",
    industry: "Technology",
    size: "10-50",
    website: "https://teamcheck.example.com",
    address: "123 Tech Street, San Francisco, CA",
    phone: "+****************",
    email: "<EMAIL>",
  });

  const [profileSettings, setProfileSettings] = useState({
    name: "Admin User",
    email: "<EMAIL>",
    phone: "+****************",
    jobTitle: "Team Manager",
    department: "Engineering",
    slackUsername: "@admin",
    bio: "Team manager with 5+ years of experience.",
    activeProfileTab: "basic",
  });

  const [attendanceSettings, setAttendanceSettings] = useState({
    checkInStart: "08:00",
    checkOutEnd: "18:00",
    graceCheckInPeriod: 15, // minutes
    graceCheckOutPeriod: 15, // minutes
    weekendDays: [0, 6], // Sunday, Saturday
    checkInMessageFormat: "Projects: {projects}, Plan: {plan}",
    checkOutMessageFormat:
      "Projects: {projects}, Completed: {completed}, Video: {video_url}",
    activeAttendanceTab: "times",
  });

  const [leaveSettings, setLeaveSettings] = useState({
    maxLeaveDays: 20,
    leaveNoticePeriod: 7,
    autoApproveAfter: 3, // days
    leaveTypes: ["Vacation", "Sick", "Personal", "Bereavement", "Other"],
    carryOverDays: 5,
    activeLeaveTab: "policy",
    bulkLeave: {
      selectedMembers: [],
      leaveName: "",
      startDate: "",
      endDate: "",
      startTime: "09:00",
      endTime: "17:00",
      leaveType: "Vacation",
    },
  });

  // Mock team members for bulk leave assignment
  const teamMembers = [
    { id: 1, name: "John Doe", department: "Engineering" },
    { id: 2, name: "Jane Smith", department: "Design" },
    { id: 3, name: "Robert Johnson", department: "Marketing" },
    { id: 4, name: "Emily Davis", department: "HR" },
    { id: 5, name: "Michael Wilson", department: "Engineering" },
    { id: 6, name: "Sarah Brown", department: "Finance" },
    { id: 7, name: "David Miller", department: "Product" },
    { id: 8, name: "Lisa Taylor", department: "Engineering" },
  ];

  const [taskSettings, setTaskSettings] = useState({
    enableKanbanView: true,
    defaultTaskView: "list",
    enableMilestones: true,
    enableTaskComments: true,
    enableTaskAttachments: false,
    notifyOnTaskAssignment: true,
    notifyOnTaskStatusChange: true,
    defaultTaskStatus: "Not Started",
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    slackNotifications: true,
    dailySummary: true,
    dailySummaryTime: "18:00",
    weeklySummary: true,
    weeklySummaryDay: 5, // Friday
    notifyOnLeaveRequest: true,
    notifyOnLeaveApproval: true,
    notifyOnCheckInOut: false,
  });

  // State for adding new leave types
  const [newLeaveType, setNewLeaveType] = useState("");

  // State for integrations tab
  const [integrationsSearchTerm, setIntegrationsSearchTerm] = useState("");
  const [activeIntegrationCategory, setActiveIntegrationCategory] =
    useState("all");

  // State for billing tab
  const [billingTab, setBillingTab] = useState("subscription");

  // Mock integration data
  const integrationCategories = [
    { id: "all", name: "All Integrations" },
    { id: "communication", name: "Communication" },
    { id: "project_management", name: "Project Management" },
    { id: "finance", name: "Finance" },
    { id: "automation", name: "Automation" },
    { id: "storage", name: "Storage" },
  ];

  const integrations = [
    {
      id: "slack",
      name: "Slack",
      description:
        "Connect with Slack to send notifications and updates directly to your workspace.",
      icon: "/integrations/slack.svg",
      status: "coming_soon",
      category: "communication",
    },
    {
      id: "whatsapp",
      name: "WhatsApp",
      description: "Send attendance and leave notifications via WhatsApp.",
      icon: "/integrations/whatsapp.svg",
      status: "coming_soon",
      category: "communication",
    },
    {
      id: "discord",
      name: "Discord",
      description:
        "Connect with Discord to receive notifications and updates in your server.",
      icon: "/integrations/discord.svg",
      status: "coming_soon",
      category: "communication",
    },
    {
      id: "jira",
      name: "Jira",
      description:
        "Sync tasks between your app and Jira for seamless project management.",
      icon: "/integrations/jira.svg",
      status: "coming_soon",
      category: "project_management",
    },
    {
      id: "stripe",
      name: "Stripe",
      description:
        "Process payments and manage subscriptions with Stripe integration.",
      icon: "/integrations/stripe.svg",
      status: "coming_soon",
      category: "finance",
    },
    {
      id: "zapier",
      name: "Zapier",
      description: "Connect with thousands of apps through Zapier automations.",
      icon: "/integrations/zapier.svg",
      status: "coming_soon",
      category: "automation",
    },
  ];

  // Filter integrations based on category and search term
  const filteredIntegrations = integrations.filter((integration) => {
    const matchesCategory =
      activeIntegrationCategory === "all" ||
      integration.category === activeIntegrationCategory;
    const matchesSearch =
      integration.name
        .toLowerCase()
        .includes(integrationsSearchTerm.toLowerCase()) ||
      integration.description
        .toLowerCase()
        .includes(integrationsSearchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Mock subscription data
  const subscriptionData = {
    plan: "Professional",
    status: "active",
    nextBillingDate: "2023-08-15",
    amount: 49.99,
    billingCycle: "monthly",
    teamMembers: 23,
    teamMembersLimit: 50,
    features: [
      "Up to 50 team members",
      "Unlimited projects",
      "Advanced reporting",
      "Time tracking",
      "Leave management",
      "Priority support",
    ],
  };

  // Calculate progress for team members usage
  const teamMembersPercentage = Math.round(
    (subscriptionData.teamMembers / subscriptionData.teamMembersLimit) * 100
  );

  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Generic change handler
  const handleChange = (setter) => (e) => {
    const { name, value, type, checked } = e.target;
    setter((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  // Form submission handlers
  const handleGeneralSubmit = (e) => {
    e.preventDefault();
    console.log("General settings saved:", generalSettings);
  };

  const handleCompanySubmit = (e) => {
    e.preventDefault();
    console.log("Company settings saved:", companySettings);
  };

  const handleProfileSubmit = (e) => {
    e.preventDefault();
    console.log("Profile settings saved:", profileSettings);
  };

  const handleAttendanceSubmit = (e) => {
    e.preventDefault();
    console.log("Attendance settings saved:", attendanceSettings);
  };

  const handleLeaveSubmit = (e) => {
    e.preventDefault();
    console.log("Leave settings saved:", leaveSettings);
  };

  const handleTaskSubmit = (e) => {
    e.preventDefault();
    console.log("Task settings saved:", taskSettings);
  };

  const handleNotificationSubmit = (e) => {
    e.preventDefault();
    console.log("Notification settings saved:", notificationSettings);
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Settings</h2>
          <p className="mt-1 text-sm text-text-secondary">
            Configure your team management preferences
          </p>
        </div>
      </div>

      {/* Settings tabs */}
      <div className="border-b border-border">
        <div className="flex overflow-x-auto hide-scrollbar">
          <nav className="flex space-x-1" aria-label="Settings Tabs">
            <button
              onClick={() => changeTab("general")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "general"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Settings className="h-4 w-4 inline mr-2" />
              General
            </button>

            <button
              onClick={() => changeTab("company")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "company"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Building className="h-4 w-4 inline mr-2" />
              Company Profile
            </button>

            <button
              onClick={() => changeTab("profile")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "profile"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <User className="h-4 w-4 inline mr-2" />
              Personal Profile
            </button>

            <button
              onClick={() => changeTab("attendance")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "attendance"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Clock className="h-4 w-4 inline mr-2" />
              Attendance
            </button>

            <button
              onClick={() => changeTab("leave")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "leave"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Calendar className="h-4 w-4 inline mr-2" />
              Leave Management
            </button>

            <button
              onClick={() => changeTab("activities")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "activities"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <CheckSquare className="h-4 w-4 inline mr-2" />
              Project Activities
            </button>

            <button
              onClick={() => changeTab("notifications")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "notifications"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Bell className="h-4 w-4 inline mr-2" />
              Notifications
            </button>

            <button
              onClick={() => changeTab("integrations")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "integrations"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <Link2 className="h-4 w-4 inline mr-2" />
              Integrations
            </button>

            <button
              onClick={() => changeTab("billing")}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === "billing"
                  ? "border-accent-1 text-white"
                  : "border-transparent text-text-secondary hover:text-white hover:border-border"
              }`}
            >
              <CreditCard className="h-4 w-4 inline mr-2" />
              Billing
            </button>
          </nav>
        </div>
      </div>

      {/* Settings form */}
      <div className="glass-card rounded-lg overflow-hidden">
        <div className="px-5 py-4 bg-surface-2 border-b border-border flex items-center">
          {activeTab === "general" && (
            <>
              <Settings className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                General Settings
              </h3>
            </>
          )}
          {activeTab === "company" && (
            <>
              <Building className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Company Profile
              </h3>
            </>
          )}
          {activeTab === "profile" && (
            <>
              <User className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Personal Profile
              </h3>
            </>
          )}
          {activeTab === "attendance" && (
            <>
              <Clock className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Attendance Settings
              </h3>
            </>
          )}
          {activeTab === "leave" && (
            <>
              <Calendar className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Leave Management Settings
              </h3>
            </>
          )}
          {activeTab === "activities" && (
            <>
              <CheckSquare className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Project Activities Settings
              </h3>
            </>
          )}
          {activeTab === "notifications" && (
            <>
              <Bell className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Notification Settings
              </h3>
            </>
          )}
          {activeTab === "integrations" && (
            <>
              <Link2 className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">Integrations</h3>
            </>
          )}
          {activeTab === "billing" && (
            <>
              <CreditCard className="h-5 w-5 text-text-secondary mr-2" />
              <h3 className="text-lg font-medium text-white">
                Billing & Subscription
              </h3>
            </>
          )}
        </div>

        {/* General Settings Form */}
        {activeTab === "general" && (
          <form onSubmit={handleGeneralSubmit} className="p-5 space-y-6">
            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Application Settings
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="timezone"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Default Timezone
                  </label>
                  <select
                    id="timezone"
                    name="timezone"
                    value={generalSettings.timezone}
                    onChange={handleChange(setGeneralSettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="UTC">
                      UTC (Coordinated Universal Time)
                    </option>
                    <option value="America/New_York">
                      EST (Eastern Standard Time)
                    </option>
                    <option value="America/Chicago">
                      CST (Central Standard Time)
                    </option>
                    <option value="America/Denver">
                      MST (Mountain Standard Time)
                    </option>
                    <option value="America/Los_Angeles">
                      PST (Pacific Standard Time)
                    </option>
                    <option value="Europe/London">
                      GMT (Greenwich Mean Time)
                    </option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="language"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Language
                  </label>
                  <select
                    id="language"
                    name="language"
                    value={generalSettings.language}
                    onChange={handleChange(setGeneralSettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="en">English</option>
                    <option value="fr">French</option>
                    <option value="es">Spanish</option>
                    <option value="de">German</option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="dateFormat"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Date Format
                  </label>
                  <select
                    id="dateFormat"
                    name="dateFormat"
                    value={generalSettings.dateFormat}
                    onChange={handleChange(setGeneralSettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="timeFormat"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Time Format
                  </label>
                  <select
                    id="timeFormat"
                    name="timeFormat"
                    value={generalSettings.timeFormat}
                    onChange={handleChange(setGeneralSettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="12h">12-hour (AM/PM)</option>
                    <option value="24h">24-hour</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </button>
            </div>
          </form>
        )}

        {/* Company Profile Form */}
        {activeTab === "company" && (
          <form onSubmit={handleCompanySubmit} className="p-5 space-y-6">
            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Company Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="companyName"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Company Name
                  </label>
                  <input
                    type="text"
                    id="companyName"
                    name="companyName"
                    value={companySettings.companyName}
                    onChange={handleChange(setCompanySettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  />
                </div>
                <div>
                  <label
                    htmlFor="industry"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Industry
                  </label>
                  <select
                    id="industry"
                    name="industry"
                    value={companySettings.industry}
                    onChange={handleChange(setCompanySettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="Technology">Technology</option>
                    <option value="Finance">Finance</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Education">Education</option>
                    <option value="Retail">Retail</option>
                    <option value="Manufacturing">Manufacturing</option>
                    <option value="Other">Other</option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="size"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Company Size
                  </label>
                  <select
                    id="size"
                    name="size"
                    value={companySettings.size}
                    onChange={handleChange(setCompanySettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="1-9">1-9 employees</option>
                    <option value="10-50">10-50 employees</option>
                    <option value="51-200">51-200 employees</option>
                    <option value="201-500">201-500 employees</option>
                    <option value="501+">501+ employees</option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="website"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Website
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Globe size={16} className="text-soft-blue opacity-70" />
                    </div>
                    <input
                      type="url"
                      id="website"
                      name="website"
                      value={companySettings.website}
                      onChange={handleChange(setCompanySettings)}
                      className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      placeholder="https://example.com"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Contact Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="address"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Address
                  </label>
                  <input
                    type="text"
                    id="address"
                    name="address"
                    value={companySettings.address}
                    onChange={handleChange(setCompanySettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  />
                </div>
                <div>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Phone Number
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Phone
                        size={16}
                        className="text-soft-purple opacity-70"
                      />
                    </div>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={companySettings.phone}
                      onChange={handleChange(setCompanySettings)}
                      className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      placeholder="+****************"
                    />
                  </div>
                </div>
                <div className="md:col-span-2">
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Email Address
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Mail size={16} className="text-soft-teal opacity-70" />
                    </div>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={companySettings.email}
                      onChange={handleChange(setCompanySettings)}
                      className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Company Profile
              </button>
            </div>
          </form>
        )}

        {/* Personal Profile Form */}
        {activeTab === "profile" && (
          <form onSubmit={handleProfileSubmit} className="p-5 space-y-6">
            {/* Profile Settings Tabs */}
            <div className="border-b border-border mb-6">
              <div className="flex">
                <button
                  type="button"
                  onClick={() =>
                    setProfileSettings((prev) => ({
                      ...prev,
                      activeProfileTab: "basic",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    profileSettings.activeProfileTab === "basic"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <User className="h-4 w-4 inline mr-2" />
                  Basic Information
                </button>

                <button
                  type="button"
                  onClick={() =>
                    setProfileSettings((prev) => ({
                      ...prev,
                      activeProfileTab: "work",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    profileSettings.activeProfileTab === "work"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <Briefcase className="h-4 w-4 inline mr-2" />
                  Work Information
                </button>

                <button
                  type="button"
                  onClick={() =>
                    setProfileSettings((prev) => ({
                      ...prev,
                      activeProfileTab: "security",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    profileSettings.activeProfileTab === "security"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <Shield className="h-4 w-4 inline mr-2" />
                  Security
                </button>
              </div>
            </div>

            {/* Basic Information Tab */}
            {profileSettings.activeProfileTab === "basic" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Basic Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="name"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={profileSettings.name}
                      onChange={handleChange(setProfileSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="email"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail size={16} className="text-soft-blue opacity-70" />
                      </div>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={profileSettings.email}
                        onChange={handleChange(setProfileSettings)}
                        className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Phone Number
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Phone
                          size={16}
                          className="text-soft-purple opacity-70"
                        />
                      </div>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={profileSettings.phone}
                        onChange={handleChange(setProfileSettings)}
                        className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="slackUsername"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Slack Username
                    </label>
                    <input
                      type="text"
                      id="slackUsername"
                      name="slackUsername"
                      value={profileSettings.slackUsername}
                      onChange={handleChange(setProfileSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                      placeholder="@username"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Work Information Tab */}
            {profileSettings.activeProfileTab === "work" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Work Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="jobTitle"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Position
                    </label>
                    <input
                      type="text"
                      id="jobTitle"
                      name="jobTitle"
                      value={profileSettings.jobTitle}
                      onChange={handleChange(setProfileSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="department"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Department
                    </label>
                    <input
                      type="text"
                      id="department"
                      name="department"
                      value={profileSettings.department}
                      onChange={handleChange(setProfileSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label
                      htmlFor="bio"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Bio
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      rows="3"
                      value={profileSettings.bio}
                      onChange={handleChange(setProfileSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                      placeholder="A brief description about yourself"
                    ></textarea>
                  </div>
                </div>
              </div>
            )}

            {/* Security Tab */}
            {profileSettings.activeProfileTab === "security" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Security
                </h4>
                <div className="space-y-4">
                  <button
                    type="button"
                    className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm flex items-center"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Change Password
                  </button>
                </div>
              </div>
            )}

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Profile
              </button>
            </div>
          </form>
        )}

        {/* Attendance Settings Form */}
        {activeTab === "attendance" && (
          <form onSubmit={handleAttendanceSubmit} className="p-5 space-y-6">
            {/* Attendance Settings Tabs */}
            <div className="border-b border-border mb-6">
              <div className="flex">
                <button
                  type="button"
                  onClick={() =>
                    setAttendanceSettings((prev) => ({
                      ...prev,
                      activeAttendanceTab: "times",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    attendanceSettings.activeAttendanceTab === "times"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <Clock className="h-4 w-4 inline mr-2" />
                  Working Hours
                </button>

                <button
                  type="button"
                  onClick={() =>
                    setAttendanceSettings((prev) => ({
                      ...prev,
                      activeAttendanceTab: "messages",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    attendanceSettings.activeAttendanceTab === "messages"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <MessageSquare className="h-4 w-4 inline mr-2" />
                  Message Formats
                </button>

                <button
                  type="button"
                  onClick={() =>
                    setAttendanceSettings((prev) => ({
                      ...prev,
                      activeAttendanceTab: "schedule",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    attendanceSettings.activeAttendanceTab === "schedule"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <Calendar className="h-4 w-4 inline mr-2" />
                  Work Schedule
                </button>
              </div>
            </div>

            {/* Working Hours Tab */}
            {attendanceSettings.activeAttendanceTab === "times" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Check-in/Check-out Times
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="checkInStart"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Check-in Start Time
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Clock
                          size={16}
                          className="text-soft-blue opacity-70"
                        />
                      </div>
                      <input
                        type="time"
                        id="checkInStart"
                        name="checkInStart"
                        value={attendanceSettings.checkInStart}
                        onChange={handleChange(setAttendanceSettings)}
                        className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="checkOutEnd"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Check-out End Time
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Clock
                          size={16}
                          className="text-soft-purple opacity-70"
                        />
                      </div>
                      <input
                        type="time"
                        id="checkOutEnd"
                        name="checkOutEnd"
                        value={attendanceSettings.checkOutEnd}
                        onChange={handleChange(setAttendanceSettings)}
                        className="input-dark block w-full pl-10 pr-3 py-2 rounded-lg text-white text-sm"
                      />
                    </div>
                  </div>
                  <div>
                    <label
                      htmlFor="graceCheckInPeriod"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Grace Period for Check-in (minutes)
                    </label>
                    <input
                      type="number"
                      id="graceCheckInPeriod"
                      name="graceCheckInPeriod"
                      value={attendanceSettings.graceCheckInPeriod}
                      onChange={handleChange(setAttendanceSettings)}
                      min="0"
                      max="60"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="graceCheckOutPeriod"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Grace Period for Check-out (minutes)
                    </label>
                    <input
                      type="number"
                      id="graceCheckOutPeriod"
                      name="graceCheckOutPeriod"
                      value={attendanceSettings.graceCheckOutPeriod}
                      onChange={handleChange(setAttendanceSettings)}
                      min="0"
                      max="60"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Message Formats Tab */}
            {attendanceSettings.activeAttendanceTab === "messages" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Check-in/Check-out Message Formats
                </h4>
                <div className="space-y-6">
                  <div>
                    <label
                      htmlFor="checkInMessageFormat"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Check-in Message Format
                    </label>
                    <textarea
                      id="checkInMessageFormat"
                      name="checkInMessageFormat"
                      value={attendanceSettings.checkInMessageFormat}
                      onChange={handleChange(setAttendanceSettings)}
                      rows="3"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                      placeholder="Projects: {projects}, Plan: {plan}"
                    ></textarea>
                    <p className="mt-1 text-xs text-text-secondary">
                      Use {"{projects}"}, {"{plan}"} as placeholders that
                      employees will fill in
                    </p>
                  </div>

                  <div>
                    <label
                      htmlFor="checkOutMessageFormat"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Check-out Message Format
                    </label>
                    <textarea
                      id="checkOutMessageFormat"
                      name="checkOutMessageFormat"
                      value={attendanceSettings.checkOutMessageFormat}
                      onChange={handleChange(setAttendanceSettings)}
                      rows="3"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                      placeholder="Projects: {projects}, Completed: {completed}, Video: {video_url}"
                    ></textarea>
                    <p className="mt-1 text-xs text-text-secondary">
                      Use {"{projects}"}, {"{completed}"}, {"{video_url}"} as
                      placeholders that employees will fill in
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Work Schedule Tab */}
            {attendanceSettings.activeAttendanceTab === "schedule" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Work Schedule
                </h4>
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-white mb-2">
                    Weekend Days
                  </label>
                  <div className="grid grid-cols-7 gap-2">
                    {[
                      "Sunday",
                      "Monday",
                      "Tuesday",
                      "Wednesday",
                      "Thursday",
                      "Friday",
                      "Saturday",
                    ].map((day, index) => (
                      <div
                        key={day}
                        className={`cursor-pointer p-2 text-center rounded-lg border ${
                          attendanceSettings.weekendDays.includes(index)
                            ? "bg-surface-3 border-accent-1 text-white"
                            : "border-border text-text-secondary hover:border-text-secondary"
                        }`}
                        onClick={() => {
                          const newWeekendDays = [
                            ...attendanceSettings.weekendDays,
                          ];
                          if (newWeekendDays.includes(index)) {
                            // Remove day
                            const dayIndex = newWeekendDays.indexOf(index);
                            newWeekendDays.splice(dayIndex, 1);
                          } else {
                            // Add day
                            newWeekendDays.push(index);
                          }
                          setAttendanceSettings((prev) => ({
                            ...prev,
                            weekendDays: newWeekendDays,
                          }));
                        }}
                      >
                        <span className="text-xs">{day.substring(0, 3)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Attendance Settings
              </button>
            </div>
          </form>
        )}

        {/* Leave Management Settings Form */}
        {activeTab === "leave" && (
          <form onSubmit={handleLeaveSubmit} className="p-5 space-y-6">
            {/* Leave Settings Tabs */}
            <div className="border-b border-border mb-6">
              <div className="flex">
                <button
                  type="button"
                  onClick={() =>
                    setLeaveSettings((prev) => ({
                      ...prev,
                      activeLeaveTab: "policy",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    leaveSettings.activeLeaveTab === "policy"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <FileText className="h-4 w-4 inline mr-2" />
                  Leave Policy
                </button>

                <button
                  type="button"
                  onClick={() =>
                    setLeaveSettings((prev) => ({
                      ...prev,
                      activeLeaveTab: "types",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    leaveSettings.activeLeaveTab === "types"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <List className="h-4 w-4 inline mr-2" />
                  Leave Types
                </button>

                <button
                  type="button"
                  onClick={() =>
                    setLeaveSettings((prev) => ({
                      ...prev,
                      activeLeaveTab: "bulk",
                    }))
                  }
                  className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                    leaveSettings.activeLeaveTab === "bulk"
                      ? "border-accent-1 text-white"
                      : "border-transparent text-text-secondary hover:text-white hover:border-border"
                  }`}
                >
                  <Users className="h-4 w-4 inline mr-2" />
                  Bulk Leave
                </button>
              </div>
            </div>

            {/* Leave Policy Tab */}
            {leaveSettings.activeLeaveTab === "policy" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Leave Policy
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="maxLeaveDays"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Maximum Leave Days Per Year
                    </label>
                    <input
                      type="number"
                      id="maxLeaveDays"
                      name="maxLeaveDays"
                      value={leaveSettings.maxLeaveDays}
                      onChange={handleChange(setLeaveSettings)}
                      min="0"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="leaveNoticePeriod"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Leave Notice Period (days)
                    </label>
                    <input
                      type="number"
                      id="leaveNoticePeriod"
                      name="leaveNoticePeriod"
                      value={leaveSettings.leaveNoticePeriod}
                      onChange={handleChange(setLeaveSettings)}
                      min="0"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="carryOverDays"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Maximum Days to Carry Over to Next Year
                    </label>
                    <input
                      type="number"
                      id="carryOverDays"
                      name="carryOverDays"
                      value={leaveSettings.carryOverDays}
                      onChange={handleChange(setLeaveSettings)}
                      min="0"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                  <div>
                    <label
                      htmlFor="autoApproveAfter"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Auto-approve Leave Requests After (days)
                    </label>
                    <input
                      type="number"
                      id="autoApproveAfter"
                      name="autoApproveAfter"
                      value={leaveSettings.autoApproveAfter}
                      onChange={handleChange(setLeaveSettings)}
                      min="0"
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                    <p className="mt-1 text-xs text-text-secondary">
                      Set to 0 to disable auto-approval
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Leave Types Tab */}
            {leaveSettings.activeLeaveTab === "types" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Leave Types
                </h4>
                <div className="space-y-4">
                  <div className="glass-card p-4 rounded-lg">
                    <div className="mb-4">
                      <p className="text-sm text-white mb-2">
                        Current Leave Types:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {leaveSettings.leaveTypes.map((type, index) => (
                          <div
                            key={index}
                            className="bg-surface-3 text-white text-xs px-2 py-1 rounded-md flex items-center"
                          >
                            <span>{type}</span>
                            <button
                              type="button"
                              className="ml-2 text-text-secondary hover:text-accent-4"
                              onClick={() => {
                                const newLeaveTypes = [
                                  ...leaveSettings.leaveTypes,
                                ];
                                newLeaveTypes.splice(index, 1);
                                setLeaveSettings((prev) => ({
                                  ...prev,
                                  leaveTypes: newLeaveTypes,
                                }));
                              }}
                            >
                              <X size={12} />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="flex">
                      <input
                        type="text"
                        id="newLeaveType"
                        placeholder="Add new leave type..."
                        className="input-dark block w-full py-2 px-3 rounded-l-lg text-white text-sm"
                        value={newLeaveType || ""}
                        onChange={(e) => setNewLeaveType(e.target.value)}
                      />
                      <button
                        type="button"
                        className="px-3 py-2 bg-surface-3 text-white rounded-r-lg hover:bg-surface-2"
                        onClick={() => {
                          if (newLeaveType && newLeaveType.trim() !== "") {
                            setLeaveSettings((prev) => ({
                              ...prev,
                              leaveTypes: [
                                ...prev.leaveTypes,
                                newLeaveType.trim(),
                              ],
                            }));
                            setNewLeaveType("");
                          }
                        }}
                      >
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Bulk Leave Tab */}
            {leaveSettings.activeLeaveTab === "bulk" && (
              <div>
                <h4 className="text-md font-medium text-white mb-4">
                  Assign Leave to Multiple Team Members
                </h4>
                <div className="space-y-6">
                  {/* Leave Details */}
                  <div className="glass-card p-4 rounded-lg">
                    <h5 className="text-sm font-medium text-white mb-3">
                      Leave Details
                    </h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="leaveName"
                          className="block text-sm font-medium text-white mb-1"
                        >
                          Leave Name
                        </label>
                        <input
                          type="text"
                          id="leaveName"
                          name="leaveName"
                          value={leaveSettings.bulkLeave.leaveName}
                          onChange={(e) =>
                            setLeaveSettings((prev) => ({
                              ...prev,
                              bulkLeave: {
                                ...prev.bulkLeave,
                                leaveName: e.target.value,
                              },
                            }))
                          }
                          placeholder="e.g., Company Retreat, Public Holiday"
                          className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="leaveType"
                          className="block text-sm font-medium text-white mb-1"
                        >
                          Leave Type
                        </label>
                        <select
                          id="leaveType"
                          name="leaveType"
                          value={leaveSettings.bulkLeave.leaveType}
                          onChange={(e) =>
                            setLeaveSettings((prev) => ({
                              ...prev,
                              bulkLeave: {
                                ...prev.bulkLeave,
                                leaveType: e.target.value,
                              },
                            }))
                          }
                          className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                        >
                          {leaveSettings.leaveTypes.map((type) => (
                            <option key={type} value={type}>
                              {type}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label
                          htmlFor="startDate"
                          className="block text-sm font-medium text-white mb-1"
                        >
                          Start Date
                        </label>
                        <input
                          type="date"
                          id="startDate"
                          name="startDate"
                          value={leaveSettings.bulkLeave.startDate}
                          onChange={(e) =>
                            setLeaveSettings((prev) => ({
                              ...prev,
                              bulkLeave: {
                                ...prev.bulkLeave,
                                startDate: e.target.value,
                              },
                            }))
                          }
                          className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="endDate"
                          className="block text-sm font-medium text-white mb-1"
                        >
                          End Date
                        </label>
                        <input
                          type="date"
                          id="endDate"
                          name="endDate"
                          value={leaveSettings.bulkLeave.endDate}
                          onChange={(e) =>
                            setLeaveSettings((prev) => ({
                              ...prev,
                              bulkLeave: {
                                ...prev.bulkLeave,
                                endDate: e.target.value,
                              },
                            }))
                          }
                          className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="startTime"
                          className="block text-sm font-medium text-white mb-1"
                        >
                          Start Time
                        </label>
                        <input
                          type="time"
                          id="startTime"
                          name="startTime"
                          value={leaveSettings.bulkLeave.startTime}
                          onChange={(e) =>
                            setLeaveSettings((prev) => ({
                              ...prev,
                              bulkLeave: {
                                ...prev.bulkLeave,
                                startTime: e.target.value,
                              },
                            }))
                          }
                          className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="endTime"
                          className="block text-sm font-medium text-white mb-1"
                        >
                          End Time
                        </label>
                        <input
                          type="time"
                          id="endTime"
                          name="endTime"
                          value={leaveSettings.bulkLeave.endTime}
                          onChange={(e) =>
                            setLeaveSettings((prev) => ({
                              ...prev,
                              bulkLeave: {
                                ...prev.bulkLeave,
                                endTime: e.target.value,
                              },
                            }))
                          }
                          className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Team Member Selection */}
                  <div className="glass-card p-4 rounded-lg">
                    <h5 className="text-sm font-medium text-white mb-3">
                      Select Team Members
                    </h5>
                    <div className="mb-4">
                      <div className="flex items-center mb-3">
                        <input
                          type="checkbox"
                          id="selectAll"
                          className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                          onChange={(e) => {
                            if (e.target.checked) {
                              setLeaveSettings((prev) => ({
                                ...prev,
                                bulkLeave: {
                                  ...prev.bulkLeave,
                                  selectedMembers: teamMembers.map(
                                    (member) => member.id
                                  ),
                                },
                              }));
                            } else {
                              setLeaveSettings((prev) => ({
                                ...prev,
                                bulkLeave: {
                                  ...prev.bulkLeave,
                                  selectedMembers: [],
                                },
                              }));
                            }
                          }}
                          checked={
                            leaveSettings.bulkLeave.selectedMembers.length ===
                            teamMembers.length
                          }
                        />
                        <label
                          htmlFor="selectAll"
                          className="ml-2 text-sm font-medium text-white"
                        >
                          Select All
                        </label>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {teamMembers.map((member) => (
                          <div
                            key={member.id}
                            className="flex items-center p-2 border border-border rounded-lg"
                          >
                            <input
                              type="checkbox"
                              id={`member-${member.id}`}
                              className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                              checked={leaveSettings.bulkLeave.selectedMembers.includes(
                                member.id
                              )}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setLeaveSettings((prev) => ({
                                    ...prev,
                                    bulkLeave: {
                                      ...prev.bulkLeave,
                                      selectedMembers: [
                                        ...prev.bulkLeave.selectedMembers,
                                        member.id,
                                      ],
                                    },
                                  }));
                                } else {
                                  setLeaveSettings((prev) => ({
                                    ...prev,
                                    bulkLeave: {
                                      ...prev.bulkLeave,
                                      selectedMembers:
                                        prev.bulkLeave.selectedMembers.filter(
                                          (id) => id !== member.id
                                        ),
                                    },
                                  }));
                                }
                              }}
                            />
                            <label
                              htmlFor={`member-${member.id}`}
                              className="ml-2 flex-1"
                            >
                              <div className="text-sm text-white">
                                {member.name}
                              </div>
                              <div className="text-xs text-text-secondary">
                                {member.department}
                              </div>
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <button
                        type="button"
                        className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
                        onClick={() => {
                          // In a real app, this would send the bulk leave request to the backend
                          console.log("Bulk leave assigned:", {
                            ...leaveSettings.bulkLeave,
                            selectedMembers:
                              leaveSettings.bulkLeave.selectedMembers.map(
                                (id) =>
                                  teamMembers.find((member) => member.id === id)
                              ),
                          });

                          // Reset form
                          setLeaveSettings((prev) => ({
                            ...prev,
                            bulkLeave: {
                              ...prev.bulkLeave,
                              leaveName: "",
                              startDate: "",
                              endDate: "",
                              selectedMembers: [],
                            },
                          }));

                          // Show success message (in a real app)
                          alert("Bulk leave assigned successfully!");
                        }}
                        disabled={
                          !leaveSettings.bulkLeave.leaveName ||
                          !leaveSettings.bulkLeave.startDate ||
                          !leaveSettings.bulkLeave.endDate ||
                          leaveSettings.bulkLeave.selectedMembers.length === 0
                        }
                      >
                        <Users className="h-4 w-4 mr-2" />
                        Assign Leave
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Leave Settings
              </button>
            </div>
          </form>
        )}

        {/* Project Activities Settings Form */}
        {activeTab === "activities" && (
          <form onSubmit={handleTaskSubmit} className="p-5 space-y-6">
            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Project Activities Display Settings
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="defaultTaskView"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Default Activity View
                  </label>
                  <select
                    id="defaultTaskView"
                    name="defaultTaskView"
                    value={taskSettings.defaultTaskView}
                    onChange={handleChange(setTaskSettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="list">List View</option>
                    <option value="kanban">Kanban Board</option>
                    <option value="calendar">Calendar View</option>
                  </select>
                </div>
                <div>
                  <label
                    htmlFor="defaultTaskStatus"
                    className="block text-sm font-medium text-white mb-1"
                  >
                    Default Status for New Activities
                  </label>
                  <select
                    id="defaultTaskStatus"
                    name="defaultTaskStatus"
                    value={taskSettings.defaultTaskStatus}
                    onChange={handleChange(setTaskSettings)}
                    className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                  >
                    <option value="Not Started">Not Started</option>
                    <option value="In Progress">In Progress</option>
                    <option value="Under Review">Under Review</option>
                    <option value="Completed">Completed</option>
                  </select>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Project Activities Features
              </h4>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableKanbanView"
                    name="enableKanbanView"
                    checked={taskSettings.enableKanbanView}
                    onChange={handleChange(setTaskSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="enableKanbanView"
                    className="ml-2 block text-sm text-white"
                  >
                    Enable Kanban board view for activities
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableMilestones"
                    name="enableMilestones"
                    checked={taskSettings.enableMilestones}
                    onChange={handleChange(setTaskSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="enableMilestones"
                    className="ml-2 block text-sm text-white"
                  >
                    Enable milestones for projects
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableTaskComments"
                    name="enableTaskComments"
                    checked={taskSettings.enableTaskComments}
                    onChange={handleChange(setTaskSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="enableTaskComments"
                    className="ml-2 block text-sm text-white"
                  >
                    Enable comments on activities
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="enableTaskAttachments"
                    name="enableTaskAttachments"
                    checked={taskSettings.enableTaskAttachments}
                    onChange={handleChange(setTaskSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="enableTaskAttachments"
                    className="ml-2 block text-sm text-white"
                  >
                    Enable file attachments on activities
                  </label>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Notifications
              </h4>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyOnTaskAssignment"
                    name="notifyOnTaskAssignment"
                    checked={taskSettings.notifyOnTaskAssignment}
                    onChange={handleChange(setTaskSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="notifyOnTaskAssignment"
                    className="ml-2 block text-sm text-white"
                  >
                    Notify users when they are assigned to an activity
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyOnTaskStatusChange"
                    name="notifyOnTaskStatusChange"
                    checked={taskSettings.notifyOnTaskStatusChange}
                    onChange={handleChange(setTaskSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="notifyOnTaskStatusChange"
                    className="ml-2 block text-sm text-white"
                  >
                    Notify activity owner when activity status changes
                  </label>
                </div>
              </div>
            </div>

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Project Activities Settings
              </button>
            </div>
          </form>
        )}

        {/* Notification Settings Form */}
        {activeTab === "notifications" && (
          <form onSubmit={handleNotificationSubmit} className="p-5 space-y-6">
            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Notification Channels
              </h4>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="emailNotifications"
                    name="emailNotifications"
                    checked={notificationSettings.emailNotifications}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="emailNotifications"
                    className="ml-2 block text-sm text-white"
                  >
                    Enable email notifications
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="slackNotifications"
                    name="slackNotifications"
                    checked={notificationSettings.slackNotifications}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="slackNotifications"
                    className="ml-2 block text-sm text-white"
                  >
                    Enable Slack notifications
                  </label>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Summary Reports
              </h4>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="dailySummary"
                    name="dailySummary"
                    checked={notificationSettings.dailySummary}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="dailySummary"
                    className="ml-2 block text-sm text-white"
                  >
                    Send daily summary report
                  </label>
                </div>
                {notificationSettings.dailySummary && (
                  <div className="ml-6">
                    <label
                      htmlFor="dailySummaryTime"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Time to send daily summary
                    </label>
                    <input
                      type="time"
                      id="dailySummaryTime"
                      name="dailySummaryTime"
                      value={notificationSettings.dailySummaryTime}
                      onChange={handleChange(setNotificationSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    />
                  </div>
                )}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="weeklySummary"
                    name="weeklySummary"
                    checked={notificationSettings.weeklySummary}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="weeklySummary"
                    className="ml-2 block text-sm text-white"
                  >
                    Send weekly summary report
                  </label>
                </div>
                {notificationSettings.weeklySummary && (
                  <div className="ml-6">
                    <label
                      htmlFor="weeklySummaryDay"
                      className="block text-sm font-medium text-white mb-1"
                    >
                      Day to send weekly summary
                    </label>
                    <select
                      id="weeklySummaryDay"
                      name="weeklySummaryDay"
                      value={notificationSettings.weeklySummaryDay}
                      onChange={handleChange(setNotificationSettings)}
                      className="input-dark block w-full py-2 px-3 rounded-lg text-white text-sm"
                    >
                      <option value={0}>Sunday</option>
                      <option value={1}>Monday</option>
                      <option value={2}>Tuesday</option>
                      <option value={3}>Wednesday</option>
                      <option value={4}>Thursday</option>
                      <option value={5}>Friday</option>
                      <option value={6}>Saturday</option>
                    </select>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-md font-medium text-white mb-4">
                Event Notifications
              </h4>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyOnLeaveRequest"
                    name="notifyOnLeaveRequest"
                    checked={notificationSettings.notifyOnLeaveRequest}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="notifyOnLeaveRequest"
                    className="ml-2 block text-sm text-white"
                  >
                    Notify managers when leave is requested
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyOnLeaveApproval"
                    name="notifyOnLeaveApproval"
                    checked={notificationSettings.notifyOnLeaveApproval}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="notifyOnLeaveApproval"
                    className="ml-2 block text-sm text-white"
                  >
                    Notify employees when leave is approved/rejected
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="notifyOnCheckInOut"
                    name="notifyOnCheckInOut"
                    checked={notificationSettings.notifyOnCheckInOut}
                    onChange={handleChange(setNotificationSettings)}
                    className="h-4 w-4 rounded border-border bg-surface text-accent-1 focus:ring-0 focus:ring-offset-0"
                  />
                  <label
                    htmlFor="notifyOnCheckInOut"
                    className="ml-2 block text-sm text-white"
                  >
                    Notify managers on employee check-in/out
                  </label>
                </div>
              </div>
            </div>

            {/* Submit button */}
            <div className="pt-4 border-t border-border">
              <button
                type="submit"
                className="px-4 py-2 btn-white font-medium rounded-lg text-sm flex items-center"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Notification Settings
              </button>
            </div>
          </form>
        )}

        {/* Integrations Tab */}
        {activeTab === "integrations" && (
          <div className="p-5 space-y-6">
            {/* Search and filter */}
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-1">
                <input
                  type="text"
                  placeholder="Search integrations..."
                  className="input-dark pl-4 pr-4 py-2 w-full rounded-lg"
                  value={integrationsSearchTerm}
                  onChange={(e) => setIntegrationsSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex overflow-x-auto hide-scrollbar">
                {integrationCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveIntegrationCategory(category.id)}
                    className={`px-4 py-2 text-sm font-medium whitespace-nowrap rounded-lg mr-2 ${
                      activeIntegrationCategory === category.id
                        ? "bg-surface-3 text-white"
                        : "text-text-secondary hover:text-white hover:bg-surface"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Integrations grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredIntegrations.map((integration) => (
                <div
                  key={integration.id}
                  className="glass-card p-4 rounded-lg border border-border hover:border-accent-1 transition-colors"
                >
                  <div className="flex items-center mb-3">
                    <div className="h-10 w-10 rounded-lg bg-white p-2 flex items-center justify-center mr-3">
                      <img
                        src={integration.icon}
                        alt={`${integration.name} icon`}
                        className="h-6 w-6"
                      />
                    </div>
                    <div>
                      <h3 className="text-md font-medium text-white">
                        {integration.name}
                      </h3>
                      <span className="text-xs text-blue-400 flex items-center">
                        <AlertCircle size={12} className="mr-1" /> Coming Soon
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-text-secondary mb-4">
                    {integration.description}
                  </p>
                  <button
                    className="w-full px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm flex items-center justify-center"
                    disabled={true}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Coming Soon
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Billing Tab */}
        {activeTab === "billing" && (
          <div className="p-5 space-y-6">
            {/* Billing Tabs */}
            <div className="border-b border-border mb-6">
              <div className="flex overflow-x-auto hide-scrollbar">
                <nav className="flex space-x-1" aria-label="Billing Tabs">
                  <button
                    onClick={() => setBillingTab("subscription")}
                    className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                      billingTab === "subscription"
                        ? "border-accent-1 text-white"
                        : "border-transparent text-text-secondary hover:text-white hover:border-border"
                    }`}
                  >
                    <CreditCard className="h-4 w-4 inline mr-2" />
                    Subscription
                  </button>

                  <button
                    onClick={() => setBillingTab("payment")}
                    className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                      billingTab === "payment"
                        ? "border-accent-1 text-white"
                        : "border-transparent text-text-secondary hover:text-white hover:border-border"
                    }`}
                  >
                    <CreditCard className="h-4 w-4 inline mr-2" />
                    Payment Method
                  </button>

                  <button
                    onClick={() => setBillingTab("history")}
                    className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                      billingTab === "history"
                        ? "border-accent-1 text-white"
                        : "border-transparent text-text-secondary hover:text-white hover:border-border"
                    }`}
                  >
                    <Clock className="h-4 w-4 inline mr-2" />
                    Billing History
                  </button>

                  <button
                    onClick={() => setBillingTab("plans")}
                    className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                      billingTab === "plans"
                        ? "border-accent-1 text-white"
                        : "border-transparent text-text-secondary hover:text-white hover:border-border"
                    }`}
                  >
                    <DollarSign className="h-4 w-4 inline mr-2" />
                    Plans
                  </button>
                </nav>
              </div>
            </div>

            {/* Subscription Tab */}
            {billingTab === "subscription" && (
              <div className="glass-card p-5 rounded-lg">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-white">
                      Current Plan
                    </h3>
                    <p className="text-sm text-text-secondary">
                      You are currently on the {subscriptionData.plan} plan
                    </p>
                  </div>
                  <div className="mt-3 md:mt-0">
                    <span className="px-3 py-1 text-sm rounded-full bg-green-900/30 text-green-300">
                      Active
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <div className="flex items-center mb-2">
                      <Calendar className="h-5 w-5 text-text-secondary mr-2" />
                      <h4 className="text-sm font-medium text-white">
                        Next Billing
                      </h4>
                    </div>
                    <p className="text-lg text-white">
                      {formatDate(subscriptionData.nextBillingDate)}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {subscriptionData.billingCycle} billing
                    </p>
                  </div>

                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <div className="flex items-center mb-2">
                      <DollarSign className="h-5 w-5 text-text-secondary mr-2" />
                      <h4 className="text-sm font-medium text-white">Amount</h4>
                    </div>
                    <p className="text-lg text-white">
                      ${subscriptionData.amount}
                    </p>
                    <p className="text-xs text-text-secondary">
                      per {subscriptionData.billingCycle}
                    </p>
                  </div>

                  <div className="glass-card p-4 rounded-lg bg-surface-2">
                    <div className="flex items-center mb-2">
                      <Users className="h-5 w-5 text-text-secondary mr-2" />
                      <h4 className="text-sm font-medium text-white">
                        Team Members
                      </h4>
                    </div>
                    <p className="text-lg text-white">
                      {subscriptionData.teamMembers} /{" "}
                      {subscriptionData.teamMembersLimit}
                    </p>
                    <div className="w-full bg-surface h-1.5 rounded-full mt-2">
                      <div
                        className="bg-accent-1 h-1.5 rounded-full"
                        style={{ width: `${teamMembersPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="border-t border-border pt-4">
                  <h4 className="text-sm font-medium text-white mb-3">
                    Plan Features
                  </h4>
                  <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {subscriptionData.features.map((feature, index) => (
                      <li
                        key={index}
                        className="flex items-center text-sm text-text-secondary"
                      >
                        <CheckCircle className="h-4 w-4 text-accent-1 mr-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="border-t border-border pt-4 mt-4 flex justify-end">
                  <button className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm">
                    Cancel Subscription
                  </button>
                  <button className="ml-3 px-4 py-2 btn-white font-medium rounded-lg text-sm">
                    Change Plan
                  </button>
                </div>
              </div>
            )}

            {/* Other billing tabs would go here */}
            {billingTab !== "subscription" && (
              <div className="glass-card p-5 rounded-lg text-center">
                <p className="text-text-secondary">
                  This billing section will be implemented in the next step
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SettingsPage;
