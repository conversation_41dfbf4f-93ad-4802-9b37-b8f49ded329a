import { useState, useEffect } from 'react';
import { 
  Filter, 
  ArrowUpDown, 
  Search, 
  Calendar, 
  Clock, 
  Users, 
  Download, 
  ChevronDown,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

const CheckInLogsPage = () => {
  // State for logs data
  const [logs, setLogs] = useState([]);
  // State for filtered logs
  const [filteredLogs, setFilteredLogs] = useState([]);
  // State for filter options
  const [filters, setFilters] = useState({
    status: 'all',
    dateRange: 'week',
    searchQuery: '',
    sortBy: 'date',
    sortOrder: 'desc'
  });
  // State for showing filter panel
  const [showFilters, setShowFilters] = useState(false);

  // Generate mock data for logs
  const generateMockLogs = () => {
    const users = [
      { initials: 'J<PERSON>', name: '<PERSON>', jobTitle: 'Frontend Developer' },
      { initials: '<PERSON><PERSON>', name: '<PERSON>', jobTitle: 'U<PERSON> <PERSON>' },
      { initials: '<PERSON><PERSON>', name: '<PERSON>', jobTitle: 'Backend Developer' },
      { initials: '<PERSON><PERSON>', name: '<PERSON>', jobTitle: 'Project Manager' },
      { initials: '<PERSON>', name: '<PERSON> <PERSON>', jobTitle: 'DevOps Engineer' },
      { initials: 'RL', name: '<PERSON> <PERSON>', jobTitle: 'QA Engineer' },
      { initials: 'TW', name: 'Tom <PERSON>', jobTitle: 'Product Manager' },
      { initials: 'BK', name: '<PERSON> <PERSON>', jobTitle: 'Data Scientist' },
      { initials: 'CP', name: '<PERSON>', jobTitle: 'Full Stack Developer' },
      { initials: 'DM', name: 'David Miller', jobTitle: 'Marketing Specialist' }
    ];

    const today = new Date();
    const logs = [];

    // Generate logs for the past 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Skip weekends
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) continue;

      // Generate logs for each user
      users.forEach(user => {
        // Random chance for leave or absence
        const randomStatus = Math.random();
        
        if (randomStatus > 0.9) {
          // On leave
          logs.push({
            id: `log-${date.toISOString()}-${user.initials}`,
            user,
            date: new Date(date),
            status: 'On Leave',
            checkIn: null,
            checkOut: null,
            duration: null,
            notes: 'Planned leave',
          });
        } else if (randomStatus > 0.85) {
          // Absent
          logs.push({
            id: `log-${date.toISOString()}-${user.initials}`,
            user,
            date: new Date(date),
            status: 'Absent',
            checkIn: null,
            checkOut: null,
            duration: null,
            notes: 'Unplanned absence',
          });
        } else {
          // Regular day
          // Generate random check-in time (between 8:00 AM and 9:30 AM)
          const checkInHour = Math.floor(Math.random() * 2) + 8;
          const checkInMinute = Math.floor(Math.random() * 60);
          const checkInTime = new Date(date);
          checkInTime.setHours(checkInHour, checkInMinute, 0);

          // Generate random check-out time (between 4:00 PM and 6:30 PM)
          const checkOutHour = Math.floor(Math.random() * 3) + 16;
          const checkOutMinute = Math.floor(Math.random() * 60);
          const checkOutTime = new Date(date);
          checkOutTime.setHours(checkOutHour, checkOutMinute, 0);

          // Calculate duration in hours
          const durationMs = checkOutTime - checkInTime;
          const durationHours = Math.round((durationMs / (1000 * 60 * 60)) * 10) / 10;

          // Random notes (sometimes empty)
          const noteOptions = [
            '',
            '',
            'Team meeting',
            'Client call',
            'Project deadline',
            'Training session',
            'Department meeting',
            ''
          ];
          const notes = noteOptions[Math.floor(Math.random() * noteOptions.length)];

          logs.push({
            id: `log-${date.toISOString()}-${user.initials}`,
            user,
            date: new Date(date),
            status: 'Present',
            checkIn: new Date(checkInTime),
            checkOut: new Date(checkOutTime),
            duration: durationHours,
            notes
          });
        }
      });
    }

    // Sort logs by date (newest first)
    logs.sort((a, b) => b.date - a.date);
    return logs;
  };

  // Apply filters to logs
  const applyFilters = () => {
    let result = [...logs];

    // Filter by status
    if (filters.status !== 'all') {
      result = result.filter(log => log.status === filters.status);
    }

    // Filter by date range
    const today = new Date();
    if (filters.dateRange === 'today') {
      result = result.filter(log => 
        log.date.toDateString() === today.toDateString()
      );
    } else if (filters.dateRange === 'week') {
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);
      result = result.filter(log => log.date >= weekAgo);
    } else if (filters.dateRange === 'month') {
      const monthAgo = new Date(today);
      monthAgo.setMonth(monthAgo.getMonth() - 1);
      result = result.filter(log => log.date >= monthAgo);
    }

    // Filter by search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      result = result.filter(log => 
        log.user.name.toLowerCase().includes(query) ||
        log.user.jobTitle.toLowerCase().includes(query) ||
        (log.notes && log.notes.toLowerCase().includes(query))
      );
    }

    // Sort logs
    result.sort((a, b) => {
      if (filters.sortBy === 'date') {
        return filters.sortOrder === 'asc' ? a.date - b.date : b.date - a.date;
      } else if (filters.sortBy === 'name') {
        return filters.sortOrder === 'asc' 
          ? a.user.name.localeCompare(b.user.name)
          : b.user.name.localeCompare(a.user.name);
      } else if (filters.sortBy === 'duration') {
        // Handle null durations
        if (a.duration === null && b.duration === null) return 0;
        if (a.duration === null) return filters.sortOrder === 'asc' ? -1 : 1;
        if (b.duration === null) return filters.sortOrder === 'asc' ? 1 : -1;
        return filters.sortOrder === 'asc' ? a.duration - b.duration : b.duration - a.duration;
      }
      return 0;
    });

    setFilteredLogs(result);
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Get user color based on initials
  const getUserColor = (initials) => {
    const colors = {
      'JD': 'bg-accent-1 text-black',
      'SW': 'bg-accent-2 text-black',
      'MJ': 'bg-accent-3 text-black',
      'JS': 'bg-accent-4 text-black',
      'AK': 'bg-soft-blue text-black',
      'RL': 'bg-soft-green text-black',
      'TW': 'bg-soft-purple text-black',
      'BK': 'bg-soft-orange text-black',
      'CP': 'bg-soft-red text-black',
      'DM': 'bg-soft-yellow text-black',
    };
    return colors[initials] || 'bg-gray-500 text-white';
  };

  // Get status badge color
  const getStatusBadge = (status) => {
    switch (status) {
      case 'Present':
        return (
          <span className="flex items-center px-2 py-1 rounded-full bg-accent-1/20 text-accent-1 text-xs">
            <CheckCircle size={12} className="mr-1" />
            Present
          </span>
        );
      case 'On Leave':
        return (
          <span className="flex items-center px-2 py-1 rounded-full bg-soft-purple/20 text-soft-purple text-xs">
            <Calendar size={12} className="mr-1" />
            On Leave
          </span>
        );
      case 'Absent':
        return (
          <span className="flex items-center px-2 py-1 rounded-full bg-accent-4/20 text-accent-4 text-xs">
            <XCircle size={12} className="mr-1" />
            Absent
          </span>
        );
      default:
        return (
          <span className="flex items-center px-2 py-1 rounded-full bg-gray-500/20 text-gray-400 text-xs">
            <AlertCircle size={12} className="mr-1" />
            Unknown
          </span>
        );
    }
  };

  // Initialize logs on component mount
  useEffect(() => {
    const mockLogs = generateMockLogs();
    setLogs(mockLogs);
    setFilteredLogs(mockLogs);
  }, []);

  // Apply filters when filter state changes
  useEffect(() => {
    applyFilters();
  }, [filters, logs]);

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Check-in/out Logs</h2>
          <p className="mt-1 text-sm text-text-secondary">
            Detailed insights of team check-in and check-out activities
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex space-x-2">
          <button className="btn-secondary flex items-center text-sm">
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Filters and search */}
      <div className="bg-surface-2 rounded-lg p-4">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          {/* Search */}
          <div className="relative flex-grow max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search size={16} className="text-text-secondary" />
            </div>
            <input
              type="text"
              placeholder="Search by name, job title, or notes..."
              className="input-dark pl-10 pr-4 py-2 w-full rounded-lg"
              value={filters.searchQuery}
              onChange={(e) => handleFilterChange('searchQuery', e.target.value)}
            />
          </div>

          {/* Filter buttons */}
          <div className="flex items-center space-x-2">
            <button 
              className="btn-secondary flex items-center text-sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter size={16} className="mr-2" />
              Filters
              <ChevronDown size={16} className="ml-2" />
            </button>
            <button className="btn-secondary flex items-center text-sm">
              <ArrowUpDown size={16} className="mr-2" />
              Sort
              <ChevronDown size={16} className="ml-2" />
            </button>
          </div>
        </div>

        {/* Expanded filters */}
        {showFilters && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-border">
            {/* Status filter */}
            <div>
              <label className="block text-sm font-medium text-white mb-1">Status</label>
              <select
                className="input-dark w-full rounded-lg"
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="Present">Present</option>
                <option value="On Leave">On Leave</option>
                <option value="Absent">Absent</option>
              </select>
            </div>

            {/* Date range filter */}
            <div>
              <label className="block text-sm font-medium text-white mb-1">Date Range</label>
              <select
                className="input-dark w-full rounded-lg"
                value={filters.dateRange}
                onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              >
                <option value="today">Today</option>
                <option value="week">Last 7 Days</option>
                <option value="month">Last 30 Days</option>
                <option value="all">All Time</option>
              </select>
            </div>

            {/* Sort options */}
            <div>
              <label className="block text-sm font-medium text-white mb-1">Sort By</label>
              <div className="flex space-x-2">
                <select
                  className="input-dark w-full rounded-lg"
                  value={filters.sortBy}
                  onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                >
                  <option value="date">Date</option>
                  <option value="name">Name</option>
                  <option value="duration">Duration</option>
                </select>
                <button
                  className="btn-secondary px-3"
                  onClick={() => handleFilterChange('sortOrder', filters.sortOrder === 'asc' ? 'desc' : 'asc')}
                >
                  <ArrowUpDown size={16} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Logs table */}
      <div className="bg-surface-2 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-surface-3">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Employee
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Check-in
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Check-out
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Duration
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              {filteredLogs.map((log) => (
                <tr key={log.id} className="hover:bg-surface-3/50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`h-8 w-8 rounded-full ${getUserColor(log.user.initials).split(' ')[0]} flex items-center justify-center mr-3`}>
                        <span className={`text-sm font-medium ${getUserColor(log.user.initials).split(' ')[1]}`}>{log.user.initials}</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-white">{log.user.name}</div>
                        <div className="text-xs text-text-secondary">{log.user.jobTitle}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                    {log.date.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric', year: 'numeric' })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(log.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                    {log.checkIn ? log.checkIn.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                    {log.checkOut ? log.checkOut.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                    {log.duration ? `${log.duration}h` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary truncate max-w-[200px]">
                    {log.notes || '-'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default CheckInLogsPage;
