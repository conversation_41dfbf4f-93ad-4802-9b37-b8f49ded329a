import { useState } from 'react';
import { 
  CreditCard, 
  Download, 
  ChevronRight, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  CreditCard as CardIcon,
  Calendar,
  DollarSign,
  Users
} from 'lucide-react';

// Mock subscription data
const subscription = {
  plan: 'Professional',
  status: 'active',
  nextBillingDate: '2023-08-15',
  amount: 49.99,
  billingCycle: 'monthly',
  paymentMethod: {
    type: 'credit_card',
    last4: '4242',
    expiryMonth: 12,
    expiryYear: 2024,
    brand: 'Visa'
  },
  features: [
    'Up to 50 team members',
    'Unlimited projects',
    'Advanced reporting',
    'Time tracking',
    'Leave management',
    'Priority support'
  ],
  teamMembers: 23,
  teamMembersLimit: 50
};

// Mock billing history
const billingHistory = [
  {
    id: 'inv-001',
    date: '2023-07-15',
    amount: 49.99,
    status: 'paid',
    description: 'TeamCheck Professional Plan - Monthly'
  },
  {
    id: 'inv-002',
    date: '2023-06-15',
    amount: 49.99,
    status: 'paid',
    description: 'TeamCheck Professional Plan - Monthly'
  },
  {
    id: 'inv-003',
    date: '2023-05-15',
    amount: 49.99,
    status: 'paid',
    description: 'TeamCheck Professional Plan - Monthly'
  }
];

// Available plans
const plans = [
  {
    id: 'starter',
    name: 'Starter',
    price: 19.99,
    billingCycle: 'monthly',
    teamMembersLimit: 10,
    features: [
      'Up to 10 team members',
      'Basic reporting',
      'Time tracking',
      'Leave management',
      'Email support'
    ],
    recommended: false
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 49.99,
    billingCycle: 'monthly',
    teamMembersLimit: 50,
    features: [
      'Up to 50 team members',
      'Advanced reporting',
      'Time tracking',
      'Leave management',
      'Priority support',
      'API access'
    ],
    recommended: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 99.99,
    billingCycle: 'monthly',
    teamMembersLimit: 100,
    features: [
      'Up to 100 team members',
      'Custom reporting',
      'Time tracking',
      'Leave management',
      'Dedicated support',
      'API access',
      'Custom integrations',
      'SSO authentication'
    ],
    recommended: false
  }
];

const BillingPage = () => {
  const [activeTab, setActiveTab] = useState('subscription');
  
  // Format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };
  
  // Calculate progress for team members usage
  const teamMembersPercentage = Math.round((subscription.teamMembers / subscription.teamMembersLimit) * 100);

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h2 className="text-2xl font-bold text-white">Billing & Subscription</h2>
        <p className="mt-1 text-sm text-text-secondary">
          Manage your subscription, payment methods, and billing history
        </p>
      </div>

      {/* Tabs */}
      <div className="border-b border-border">
        <div className="flex overflow-x-auto hide-scrollbar">
          <nav className="flex space-x-1" aria-label="Billing Tabs">
            <button
              onClick={() => setActiveTab('subscription')}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === 'subscription'
                  ? 'border-accent-1 text-white'
                  : 'border-transparent text-text-secondary hover:text-white hover:border-border'
              }`}
            >
              <CreditCard className="h-4 w-4 inline mr-2" />
              Subscription
            </button>
            
            <button
              onClick={() => setActiveTab('payment')}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === 'payment'
                  ? 'border-accent-1 text-white'
                  : 'border-transparent text-text-secondary hover:text-white hover:border-border'
              }`}
            >
              <CardIcon className="h-4 w-4 inline mr-2" />
              Payment Method
            </button>
            
            <button
              onClick={() => setActiveTab('history')}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === 'history'
                  ? 'border-accent-1 text-white'
                  : 'border-transparent text-text-secondary hover:text-white hover:border-border'
              }`}
            >
              <Clock className="h-4 w-4 inline mr-2" />
              Billing History
            </button>
            
            <button
              onClick={() => setActiveTab('plans')}
              className={`px-4 py-2 text-sm font-medium border-b-2 whitespace-nowrap ${
                activeTab === 'plans'
                  ? 'border-accent-1 text-white'
                  : 'border-transparent text-text-secondary hover:text-white hover:border-border'
              }`}
            >
              <DollarSign className="h-4 w-4 inline mr-2" />
              Plans
            </button>
          </nav>
        </div>
      </div>

      {/* Subscription Tab */}
      {activeTab === 'subscription' && (
        <div className="space-y-6">
          <div className="glass-card p-5 rounded-lg">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
              <div>
                <h3 className="text-lg font-medium text-white">Current Plan</h3>
                <p className="text-sm text-text-secondary">
                  You are currently on the {subscription.plan} plan
                </p>
              </div>
              <div className="mt-3 md:mt-0">
                <span className="px-3 py-1 text-sm rounded-full bg-green-900/30 text-green-300">
                  Active
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="glass-card p-4 rounded-lg bg-surface-2">
                <div className="flex items-center mb-2">
                  <Calendar className="h-5 w-5 text-text-secondary mr-2" />
                  <h4 className="text-sm font-medium text-white">Next Billing</h4>
                </div>
                <p className="text-lg text-white">{formatDate(subscription.nextBillingDate)}</p>
                <p className="text-xs text-text-secondary">{subscription.billingCycle} billing</p>
              </div>
              
              <div className="glass-card p-4 rounded-lg bg-surface-2">
                <div className="flex items-center mb-2">
                  <DollarSign className="h-5 w-5 text-text-secondary mr-2" />
                  <h4 className="text-sm font-medium text-white">Amount</h4>
                </div>
                <p className="text-lg text-white">${subscription.amount}</p>
                <p className="text-xs text-text-secondary">per {subscription.billingCycle}</p>
              </div>
              
              <div className="glass-card p-4 rounded-lg bg-surface-2">
                <div className="flex items-center mb-2">
                  <Users className="h-5 w-5 text-text-secondary mr-2" />
                  <h4 className="text-sm font-medium text-white">Team Members</h4>
                </div>
                <p className="text-lg text-white">{subscription.teamMembers} / {subscription.teamMembersLimit}</p>
                <div className="w-full bg-surface h-1.5 rounded-full mt-2">
                  <div 
                    className="bg-accent-1 h-1.5 rounded-full" 
                    style={{ width: `${teamMembersPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="border-t border-border pt-4">
              <h4 className="text-sm font-medium text-white mb-3">Plan Features</h4>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {subscription.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-sm text-text-secondary">
                    <CheckCircle className="h-4 w-4 text-accent-1 mr-2" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="border-t border-border pt-4 mt-4 flex justify-end">
              <button className="px-4 py-2 btn-white-ghost font-medium rounded-lg text-sm">
                Cancel Subscription
              </button>
              <button className="ml-3 px-4 py-2 btn-white font-medium rounded-lg text-sm">
                Change Plan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Method Tab */}
      {activeTab === 'payment' && (
        <div className="space-y-6">
          <div className="glass-card p-5 rounded-lg">
            <h3 className="text-lg font-medium text-white mb-4">Payment Method</h3>
            
            <div className="flex items-center p-4 border border-border rounded-lg mb-4">
              <div className="h-10 w-10 rounded-lg bg-surface-3 flex items-center justify-center mr-4">
                <CreditCard className="h-5 w-5 text-white" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-white">
                  {subscription.paymentMethod.brand} •••• {subscription.paymentMethod.last4}
                </p>
                <p className="text-xs text-text-secondary">
                  Expires {subscription.paymentMethod.expiryMonth}/{subscription.paymentMethod.expiryYear}
                </p>
              </div>
              <div>
                <button className="px-3 py-1 btn-white-ghost text-sm rounded-lg">
                  Edit
                </button>
              </div>
            </div>
            
            <button className="px-4 py-2 btn-white font-medium rounded-lg text-sm">
              Add Payment Method
            </button>
          </div>
        </div>
      )}

      {/* Billing History Tab */}
      {activeTab === 'history' && (
        <div className="space-y-6">
          <div className="glass-card rounded-lg overflow-hidden">
            <div className="px-5 py-4 bg-surface-2 border-b border-border flex items-center justify-between">
              <h3 className="text-lg font-medium text-white">Billing History</h3>
              <button className="px-3 py-1 btn-white-ghost text-sm rounded-lg flex items-center">
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-surface-2 border-b border-border">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Invoice
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border">
                  {billingHistory.map((invoice) => (
                    <tr key={invoice.id} className="hover:bg-surface-2">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-white">{invoice.id}</div>
                        <div className="text-xs text-text-secondary">{invoice.description}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        {formatDate(invoice.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                        ${invoice.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="px-2 py-1 text-xs rounded-full bg-green-900/30 text-green-300">
                          {invoice.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right">
                        <button className="text-text-secondary hover:text-white">
                          <Download size={16} />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Plans Tab */}
      {activeTab === 'plans' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {plans.map((plan) => (
              <div 
                key={plan.id} 
                className={`glass-card p-5 rounded-lg border ${
                  plan.recommended ? 'border-accent-1' : 'border-border'
                }`}
              >
                {plan.recommended && (
                  <div className="bg-accent-1 text-white text-xs font-medium px-2 py-1 rounded-full inline-block mb-3">
                    Recommended
                  </div>
                )}
                <h3 className="text-lg font-medium text-white">{plan.name}</h3>
                <div className="mt-2 mb-4">
                  <span className="text-2xl font-bold text-white">${plan.price}</span>
                  <span className="text-text-secondary text-sm">/{plan.billingCycle}</span>
                </div>
                
                <div className="border-t border-border pt-4 mb-4">
                  <ul className="space-y-2">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start text-sm">
                        <CheckCircle className="h-4 w-4 text-accent-1 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-text-secondary">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <button 
                  className={`w-full px-4 py-2 rounded-lg text-sm font-medium ${
                    plan.id === subscription.plan.toLowerCase() 
                      ? 'bg-surface-3 text-white cursor-default'
                      : 'btn-white'
                  }`}
                  disabled={plan.id === subscription.plan.toLowerCase()}
                >
                  {plan.id === subscription.plan.toLowerCase() ? 'Current Plan' : 'Select Plan'}
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default BillingPage;
