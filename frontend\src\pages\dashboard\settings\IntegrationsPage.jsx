import { useState } from "react";
import { Settings, ExternalLink, Check, AlertCircle } from "lucide-react";

// Import integration icons
import slackIcon from "/integrations/slack.svg";
import whatsappIcon from "/integrations/whatsapp.svg";
import discordIcon from "/integrations/discord.svg";
import jiraIcon from "/integrations/jira.svg";
import stripeIcon from "/integrations/stripe.svg";
import zapierIcon from "/integrations/zapier.svg";
import trelloIcon from "/integrations/trello.svg";
import quickbooksIcon from "/integrations/quickbooks.svg";
import asanaIcon from "/integrations/asana.svg";
import googleDriveIcon from "/integrations/google-drive.svg";

// Mock integrations data
const integrations = [
  {
    id: "slack",
    name: "Slack",
    description:
      "Connect TeamCheck with Slack to send notifications and updates directly to your workspace.",
    icon: slackIcon,
    status: "coming_soon",
    category: "communication",
  },
  {
    id: "whatsapp",
    name: "WhatsApp",
    description: "Send attendance and leave notifications via WhatsApp.",
    icon: whatsappIcon,
    status: "coming_soon",
    category: "communication",
  },
  {
    id: "discord",
    name: "Discord",
    description:
      "Connect with Discord to receive notifications and updates in your server.",
    icon: discordIcon,
    status: "coming_soon",
    category: "communication",
  },
  {
    id: "jira",
    name: "Jira",
    description:
      "Sync tasks between TeamCheck and Jira for seamless project management.",
    icon: jiraIcon,
    status: "coming_soon",
    category: "project_management",
  },
  {
    id: "stripe",
    name: "Stripe",
    description:
      "Process payments and manage subscriptions with Stripe integration.",
    icon: stripeIcon,
    status: "coming_soon",
    category: "finance",
  },
  {
    id: "zapier",
    name: "Zapier",
    description:
      "Connect TeamCheck with thousands of apps through Zapier automations.",
    icon: zapierIcon,
    status: "coming_soon",
    category: "automation",
  },
  {
    id: "trello",
    name: "Trello",
    description:
      "Sync your TeamCheck tasks with Trello boards for visual project management.",
    icon: trelloIcon,
    status: "coming_soon",
    category: "project_management",
  },
  {
    id: "quickbooks",
    name: "QuickBooks",
    description:
      "Integrate with QuickBooks for seamless accounting and payroll management.",
    icon: quickbooksIcon,
    status: "coming_soon",
    category: "finance",
  },
  {
    id: "asana",
    name: "Asana",
    description:
      "Connect TeamCheck with Asana for comprehensive project and task management.",
    icon: asanaIcon,
    status: "coming_soon",
    category: "project_management",
  },
  {
    id: "google-drive",
    name: "Google Drive",
    description:
      "Store and share documents and reports directly to Google Drive.",
    icon: googleDriveIcon,
    status: "coming_soon",
    category: "storage",
  },
];

// Categories for filtering
const categories = [
  { id: "all", name: "All Integrations" },
  { id: "communication", name: "Communication" },
  { id: "project_management", name: "Project Management" },
  { id: "finance", name: "Finance" },
  { id: "automation", name: "Automation" },
  { id: "storage", name: "Storage" },
];

const IntegrationsPage = () => {
  const [activeCategory, setActiveCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");

  // Filter integrations based on category and search term
  const filteredIntegrations = integrations.filter((integration) => {
    const matchesCategory =
      activeCategory === "all" || integration.category === activeCategory;
    const matchesSearch =
      integration.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      integration.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div>
        <h2 className="text-2xl font-bold text-white">Integrations</h2>
        <p className="mt-1 text-sm text-text-secondary">
          Connect TeamCheck with your favorite tools and services
        </p>
      </div>

      {/* Search and filter */}
      <div className="flex flex-col gap-4 md:flex-row">
        <div className="relative flex-1">
          <input
            type="text"
            placeholder="Search integrations..."
            className="py-2 pr-4 pl-4 w-full rounded-lg input-dark"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex overflow-x-auto hide-scrollbar">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`px-4 py-2 text-sm font-medium whitespace-nowrap rounded-lg mr-2 ${
                activeCategory === category.id
                  ? "bg-surface-3 text-white"
                  : "text-text-secondary hover:text-white hover:bg-surface"
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Integrations grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredIntegrations.map((integration) => (
          <div
            key={integration.id}
            className="p-4 rounded-lg border transition-colors glass-card border-border hover:border-accent-1"
          >
            <div className="flex items-center mb-3">
              <div className="flex justify-center items-center p-2 mr-3 w-10 h-10 bg-white rounded-lg">
                <img
                  src={integration.icon}
                  alt={`${integration.name} icon`}
                  className="w-6 h-6"
                />
              </div>
              <div>
                <h3 className="font-medium text-white text-md">
                  {integration.name}
                </h3>
                {integration.status === "connected" ? (
                  <span className="flex items-center text-xs text-green-400">
                    <Check size={12} className="mr-1" /> Connected
                  </span>
                ) : (
                  <span className="flex items-center text-xs text-blue-400">
                    <AlertCircle size={12} className="mr-1" /> Coming Soon
                  </span>
                )}
              </div>
            </div>
            <p className="mb-4 text-sm text-text-secondary">
              {integration.description}
            </p>
            <button
              className="flex justify-center items-center px-4 py-2 w-full text-sm font-medium rounded-lg btn-white-ghost"
              disabled={integration.status === "coming_soon"}
            >
              <ExternalLink className="mr-2 w-4 h-4" />
              {integration.status === "connected"
                ? "Manage"
                : integration.status === "coming_soon"
                ? "Coming Soon"
                : "Connect"}
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default IntegrationsPage;
