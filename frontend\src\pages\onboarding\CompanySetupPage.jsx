import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Building, Clock, Globe, ArrowRight } from 'lucide-react';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';
import { createCompany } from '../../store/slices/companiesSlice';
import { useAppDispatch, useAppSelector } from '../../store/hooks';

const CompanySetupPage = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    companyName: '',
    workingHoursStart: '09:00',
    workingHoursEnd: '17:00',
    timezone: 'UTC',
    country: ''
  });
  
  const [errors, setErrors] = useState({});
  const dispatch = useAppDispatch()

  const timezones = [
    { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
    { value: 'America/New_York', label: 'EST (Eastern Standard Time)' },
    { value: 'America/Chicago', label: 'CST (Central Standard Time)' },
    { value: 'America/Denver', label: 'MST (Mountain Standard Time)' },
    { value: 'America/Los_Angeles', label: 'PST (Pacific Standard Time)' },
    { value: 'Europe/London', label: 'GMT (Greenwich Mean Time)' },
    { value: 'Europe/Paris', label: 'CET (Central European Time)' },
    { value: 'Asia/Tokyo', label: 'JST (Japan Standard Time)' },
    { value: 'Australia/Sydney', label: 'AEST (Australian Eastern Standard Time)' }
  ];
  
  const countries = [
    { value: 'us', label: 'United States' },
    { value: 'ca', label: 'Canada' },
    { value: 'uk', label: 'United Kingdom' },
    { value: 'au', label: 'Australia' },
    { value: 'de', label: 'Germany' },
    { value: 'fr', label: 'France' },
    { value: 'jp', label: 'Japan' },
    { value: 'in', label: 'India' },
    { value: 'br', label: 'Brazil' },
    { value: 'ng', label: 'Nigeria' }
  ];
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };
  
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
    }
    
    if (!formData.workingHoursStart) {
      newErrors.workingHoursStart = 'Start time is required';
    }
    
    if (!formData.workingHoursEnd) {
      newErrors.workingHoursEnd = 'End time is required';
    }
    
    if (!formData.timezone) {
      newErrors.timezone = 'Timezone is required';
    }
    
    if (!formData.country) {
      newErrors.country = 'Country is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      const response = await dispatch(createCompany(formData));
      console.log('Company data:', formData);
      console.log(response, "is the response")
      if (createCompany.fulfilled.match(response)) {
        navigate('/onboarding/invite');
      }
    }
  };
  
  return (
    <OnboardingLayout currentStep={2}>
      <div>
        <div className="text-center mb-6">
          <h2 className="text-xl font-semibold text-white">Company Setup</h2>
          <p className="text-text-secondary mt-1">
            Tell us about your organization to customize your experience
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Company Name */}
          <div>
            <label htmlFor="companyName" className="block text-sm font-medium text-white mb-1">
              Company Name
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Building size={16} className="text-soft-blue opacity-70" />
              </div>
              <input
                id="companyName"
                name="companyName"
                type="text"
                value={formData.companyName}
                onChange={handleChange}
                className="input-dark block w-full pl-10 pr-3 py-2.5 rounded-lg text-white text-sm"
                placeholder="Acme Inc."
              />
            </div>
            {errors.companyName && (
              <p className="mt-1 text-xs text-accent-4">{errors.companyName}</p>
            )}
          </div>
          
          {/* Working Hours */}
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Working Hours
            </label>
            <div className="flex items-center space-x-4">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Clock size={16} className="text-soft-teal opacity-70" />
                </div>
                <input
                  id="workingHoursStart"
                  name="workingHoursStart"
                  type="time"
                  value={formData.workingHoursStart}
                  onChange={handleChange}
                  className="input-dark block w-full pl-10 pr-3 py-2.5 rounded-lg text-white text-sm"
                />
              </div>
              <span className="text-text-secondary">to</span>
              <div className="relative flex-1">
                <input
                  id="workingHoursEnd"
                  name="workingHoursEnd"
                  type="time"
                  value={formData.workingHoursEnd}
                  onChange={handleChange}
                  className="input-dark block w-full px-3 py-2.5 rounded-lg text-white text-sm"
                />
              </div>
            </div>
            {(errors.workingHoursStart || errors.workingHoursEnd) && (
              <p className="mt-1 text-xs text-accent-4">
                {errors.workingHoursStart || errors.workingHoursEnd}
              </p>
            )}
          </div>
          
          {/* Timezone and Country */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="timezone" className="block text-sm font-medium text-white mb-1">
                Timezone
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Globe size={16} className="text-soft-purple opacity-70" />
                </div>
                <select
                  id="timezone"
                  name="timezone"
                  value={formData.timezone}
                  onChange={handleChange}
                  className="input-dark block w-full pl-10 pr-3 py-2.5 rounded-lg text-white text-sm appearance-none"
                >
                  <option value="" disabled>Select timezone</option>
                  {timezones.map(tz => (
                    <option key={tz.value} value={tz.value}>{tz.label}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ArrowRight size={14} className="text-text-secondary transform rotate-90" />
                </div>
              </div>
              {errors.timezone && (
                <p className="mt-1 text-xs text-accent-4">{errors.timezone}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="country" className="block text-sm font-medium text-white mb-1">
                Country
              </label>
              <div className="relative">
                <select
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleChange}
                  className="input-dark block w-full px-3 py-2.5 rounded-lg text-white text-sm appearance-none"
                >
                  <option value="" disabled>Select country</option>
                  {countries.map(country => (
                    <option key={country.value} value={country.value}>{country.label}</option>
                  ))}
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <ArrowRight size={14} className="text-text-secondary transform rotate-90" />
                </div>
              </div>
              {errors.country && (
                <p className="mt-1 text-xs text-accent-4">{errors.country}</p>
              )}
            </div>
          </div>
          
          {/* Submit Button */}
          <div className="pt-4">
            <button
              type="submit"
              className="w-full py-2.5 px-4 btn-white font-medium rounded-lg text-sm flex items-center justify-center"
            >
              Continue
              <ArrowRight size={16} className="ml-2" />
            </button>
          </div>
        </form>
      </div>
    </OnboardingLayout>
  );
};

export default CompanySetupPage;
