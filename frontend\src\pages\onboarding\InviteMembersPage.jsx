import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserPlus, X, Mail, Users, ArrowRight, Trash2 } from 'lucide-react';
import OnboardingLayout from '../../components/onboarding/OnboardingLayout';

const InviteMembersPage = () => {
  const navigate = useNavigate();
  const [invites, setInvites] = useState([
    { id: 1, email: '', name: '', role: 'user' }
  ]);
  
  const roles = [
    { value: 'admin', label: 'Admin', description: 'Full access to all features and settings' },
    { value: 'manager', label: 'Manager', description: 'Can manage team members and tasks' },
    { value: 'user', label: 'Team Member', description: 'Basic access to assigned tasks' }
  ];
  
  const handleChange = (id, field, value) => {
    setInvites(prev => 
      prev.map(invite => 
        invite.id === id ? { ...invite, [field]: value } : invite
      )
    );
  };
  
  const addInvite = () => {
    const newId = Math.max(0, ...invites.map(i => i.id)) + 1;
    setInvites([...invites, { id: newId, email: '', name: '', role: 'user' }]);
  };
  
  const removeInvite = (id) => {
    if (invites.length > 1) {
      setInvites(invites.filter(invite => invite.id !== id));
    }
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Filter out empty invites
    const validInvites = invites.filter(invite => invite.email.trim() !== '');
    
    // In a real app, you would send these invites to your backend
    console.log('Invites to send:', validInvites);
    
    // Navigate to dashboard
    navigate('/dashboard');
  };
  
  const handleSkip = () => {
    navigate('/dashboard');
  };
  
  return (
    <OnboardingLayout currentStep={3}>
      <div>
        <div className="text-center mb-6">
          <h2 className="text-xl font-semibold text-white">Invite Team Members</h2>
          <p className="text-text-secondary mt-1">
            Invite your team to join TeamCheck (optional)
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            {invites.map((invite, index) => (
              <div key={invite.id} className="glass-card p-4 rounded-lg">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium text-white flex items-center">
                    <UserPlus size={16} className="mr-2 text-soft-blue" />
                    {invite.name ? invite.name : `Team Member ${index + 1}`}
                  </h3>
                  {invites.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeInvite(invite.id)}
                      className="text-text-secondary hover:text-accent-4 transition-colors"
                    >
                      <Trash2 size={16} />
                    </button>
                  )}
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Name Input */}
                  <div>
                    <label htmlFor={`name-${invite.id}`} className="block text-xs font-medium text-white mb-1">
                      Name
                    </label>
                    <input
                      id={`name-${invite.id}`}
                      type="text"
                      value={invite.name}
                      onChange={(e) => handleChange(invite.id, 'name', e.target.value)}
                      className="input-dark block w-full px-3 py-2 rounded-lg text-white text-sm"
                      placeholder="John Doe"
                    />
                  </div>
                  
                  {/* Email Input */}
                  <div>
                    <label htmlFor={`email-${invite.id}`} className="block text-xs font-medium text-white mb-1">
                      Email Address
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Mail size={14} className="text-soft-blue opacity-70" />
                      </div>
                      <input
                        id={`email-${invite.id}`}
                        type="email"
                        value={invite.email}
                        onChange={(e) => handleChange(invite.id, 'email', e.target.value)}
                        className="input-dark block w-full pl-9 pr-3 py-2 rounded-lg text-white text-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
                
                {/* Role Selection */}
                <div className="mt-3">
                  <label className="block text-xs font-medium text-white mb-2">
                    Role
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                    {roles.map(role => (
                      <div 
                        key={role.value}
                        className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                          invite.role === role.value 
                            ? 'border-accent-1 bg-surface-3' 
                            : 'border-border hover:border-text-secondary'
                        }`}
                        onClick={() => handleChange(invite.id, 'role', role.value)}
                      >
                        <div className="flex items-center">
                          <div 
                            className={`w-4 h-4 rounded-full border mr-2 flex items-center justify-center ${
                              invite.role === role.value 
                                ? 'border-accent-1' 
                                : 'border-text-secondary'
                            }`}
                          >
                            {invite.role === role.value && (
                              <div className="w-2 h-2 rounded-full bg-accent-1"></div>
                            )}
                          </div>
                          <span className="text-sm font-medium text-white">{role.label}</span>
                        </div>
                        <p className="text-xs text-text-secondary mt-1 ml-6">
                          {role.description}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Add More Button */}
          <button
            type="button"
            onClick={addInvite}
            className="w-full py-2 px-4 btn-white-ghost font-medium rounded-lg text-sm flex items-center justify-center"
          >
            <UserPlus size={16} className="mr-2" />
            Add Another Team Member
          </button>
          
          {/* Action Buttons */}
          <div className="pt-4 flex flex-col sm:flex-row sm:justify-between space-y-3 sm:space-y-0 sm:space-x-3">
            <button
              type="button"
              onClick={handleSkip}
              className="py-2.5 px-4 btn-white-outline font-medium rounded-lg text-sm"
            >
              Skip for Now
            </button>
            <button
              type="submit"
              className="py-2.5 px-4 btn-white font-medium rounded-lg text-sm flex items-center justify-center"
            >
              <span>Complete Setup</span>
              <ArrowRight size={16} className="ml-2" />
            </button>
          </div>
        </form>
      </div>
    </OnboardingLayout>
  );
};

export default InviteMembersPage;
