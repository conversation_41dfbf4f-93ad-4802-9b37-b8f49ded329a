const API_BASE_URL =
  import.meta.env.VITE_BACKEND_URL || "http://localhost:3000";

class AnalyticsService {
  // Check if we should track analytics (only in production)
  static shouldTrack() {
    return import.meta.env.PROD;
  }

  // Track page visit (only for landing page and only in production)
  static async trackPageVisit(page = "landing", referrer = null) {
    // Only track if in production and on landing page
    if (!this.shouldTrack() || page !== "landing") {
      console.log(
        "Analytics tracking disabled in development or non-landing page"
      );
      return { success: false, reason: "tracking_disabled" };
    }
    try {
      const response = await fetch(
        `${API_BASE_URL}/analytics/track/page-visit`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            page,
            referrer: referrer || document.referrer || null,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error tracking page visit:", error);
      // Don't throw error to avoid breaking the app
      return { success: false, error: error.message };
    }
  }

  // Track login button click (only in production)
  static async trackLoginClick(source = "header") {
    // Only track if in production
    if (!this.shouldTrack()) {
      console.log("Analytics tracking disabled in development");
      return { success: false, reason: "tracking_disabled" };
    }
    try {
      const response = await fetch(
        `${API_BASE_URL}/analytics/track/login-click`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            source,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error tracking login click:", error);
      return { success: false, error: error.message };
    }
  }

  // Track pricing button click (only in production)
  static async trackPricingClick(
    planName,
    planPrice = null,
    action = "buy_now"
  ) {
    // Only track if in production
    if (!this.shouldTrack()) {
      console.log("Analytics tracking disabled in development");
      return { success: false, reason: "tracking_disabled" };
    }
    try {
      const response = await fetch(
        `${API_BASE_URL}/analytics/track/pricing-click`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            planName,
            planPrice,
            action,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Error tracking pricing click:", error);
      return { success: false, error: error.message };
    }
  }

  // Submit waitlist email
  static async joinWaitlist(email, source = "landing_page") {
    try {
      const response = await fetch(`${API_BASE_URL}/waitlist/join`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          source,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.
      return data;
    } catch (error) {
      console.error("Error joining waitlist:", error);
      throw error; // Throw error for waitlist to show user feedback
    }
  }
}

export default AnalyticsService;
