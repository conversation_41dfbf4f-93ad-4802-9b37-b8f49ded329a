import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { attendanceService } from '../../api';

// Async thunks for attendance operations
export const fetchAttendanceRecords = createAsyncThunk(
  'attendance/fetchAttendanceRecords',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await attendanceService.getAttendanceRecords(params);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch attendance records'
      );
    }
  }
);

export const checkIn = createAsyncThunk(
  'attendance/checkIn',
  async (checkInData, { rejectWithValue }) => {
    try {
      const response = await attendanceService.checkIn(checkInData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to check in'
      );
    }
  }
);

export const checkOut = createAsyncThunk(
  'attendance/checkOut',
  async (checkOutData, { rejectWithValue }) => {
    try {
      const response = await attendanceService.checkOut(checkOutData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to check out'
      );
    }
  }
);

export const fetchDailyOverview = createAsyncThunk(
  'attendance/fetchDailyOverview',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await attendanceService.getDailyOverview(params);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch daily overview'
      );
    }
  }
);

export const fetchDetailedLogs = createAsyncThunk(
  'attendance/fetchDetailedLogs',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await attendanceService.getDetailedLogs(params);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch detailed logs'
      );
    }
  }
);

export const fetchHoursBreakdown = createAsyncThunk(
  'attendance/fetchHoursBreakdown',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await attendanceService.getHoursBreakdown(params);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch hours breakdown'
      );
    }
  }
);

// Initial state
const initialState = {
  attendanceRecords: [],
  dailyOverview: null,
  detailedLogs: [],
  hoursBreakdown: null,
  currentStatus: null, // 'checked-in' | 'checked-out' | null
  loading: false,
  error: null,
  checkInLoading: false,
  checkOutLoading: false,
  overviewLoading: false,
  logsLoading: false,
  hoursLoading: false,
};

// Attendance slice
const attendanceSlice = createSlice({
  name: 'attendance',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentStatus: (state, action) => {
      state.currentStatus = action.payload;
    },
    clearAttendanceData: (state) => {
      state.attendanceRecords = [];
      state.dailyOverview = null;
      state.detailedLogs = [];
      state.hoursBreakdown = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch attendance records
    builder
      .addCase(fetchAttendanceRecords.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAttendanceRecords.fulfilled, (state, action) => {
        state.loading = false;
        state.attendanceRecords = action.payload.records || action.payload;
        state.error = null;
      })
      .addCase(fetchAttendanceRecords.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Check in
    builder
      .addCase(checkIn.pending, (state) => {
        state.checkInLoading = true;
        state.error = null;
      })
      .addCase(checkIn.fulfilled, (state, action) => {
        state.checkInLoading = false;
        state.currentStatus = 'checked-in';
        state.error = null;
      })
      .addCase(checkIn.rejected, (state, action) => {
        state.checkInLoading = false;
        state.error = action.payload;
      })
      
    // Check out
    builder
      .addCase(checkOut.pending, (state) => {
        state.checkOutLoading = true;
        state.error = null;
      })
      .addCase(checkOut.fulfilled, (state, action) => {
        state.checkOutLoading = false;
        state.currentStatus = 'checked-out';
        state.error = null;
      })
      .addCase(checkOut.rejected, (state, action) => {
        state.checkOutLoading = false;
        state.error = action.payload;
      })
      
    // Fetch daily overview
    builder
      .addCase(fetchDailyOverview.pending, (state) => {
        state.overviewLoading = true;
        state.error = null;
      })
      .addCase(fetchDailyOverview.fulfilled, (state, action) => {
        state.overviewLoading = false;
        state.dailyOverview = action.payload;
        state.error = null;
      })
      .addCase(fetchDailyOverview.rejected, (state, action) => {
        state.overviewLoading = false;
        state.error = action.payload;
      })
      
    // Fetch detailed logs
    builder
      .addCase(fetchDetailedLogs.pending, (state) => {
        state.logsLoading = true;
        state.error = null;
      })
      .addCase(fetchDetailedLogs.fulfilled, (state, action) => {
        state.logsLoading = false;
        state.detailedLogs = action.payload.logs || action.payload;
        state.error = null;
      })
      .addCase(fetchDetailedLogs.rejected, (state, action) => {
        state.logsLoading = false;
        state.error = action.payload;
      })
      
    // Fetch hours breakdown
    builder
      .addCase(fetchHoursBreakdown.pending, (state) => {
        state.hoursLoading = true;
        state.error = null;
      })
      .addCase(fetchHoursBreakdown.fulfilled, (state, action) => {
        state.hoursLoading = false;
        state.hoursBreakdown = action.payload;
        state.error = null;
      })
      .addCase(fetchHoursBreakdown.rejected, (state, action) => {
        state.hoursLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, setCurrentStatus, clearAttendanceData } = attendanceSlice.actions;
export default attendanceSlice.reducer;
