import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { leavesService } from '../../api';

// Async thunks for leaves operations
export const fetchLeaveRequests = createAsyncThunk(
  'leaves/fetchLeaveRequests',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await leavesService.getLeaveRequests(params);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch leave requests'
      );
    }
  }
);

export const createLeaveRequest = createAsyncThunk(
  'leaves/createLeaveRequest',
  async (leaveData, { rejectWithValue }) => {
    try {
      const response = await leavesService.createLeaveRequest(leaveData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to create leave request'
      );
    }
  }
);

export const fetchLeaveById = createAsyncThunk(
  'leaves/fetchLeaveById',
  async (leaveId, { rejectWithValue }) => {
    try {
      const response = await leavesService.getLeaveById(leaveId);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch leave request'
      );
    }
  }
);

export const updateLeaveRequest = createAsyncThunk(
  'leaves/updateLeaveRequest',
  async ({ id, leaveData }, { rejectWithValue }) => {
    try {
      const response = await leavesService.updateLeaveRequest(id, leaveData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update leave request'
      );
    }
  }
);

export const approveLeave = createAsyncThunk(
  'leaves/approveLeave',
  async ({ id, approvalData }, { rejectWithValue }) => {
    try {
      const response = await leavesService.approveLeave(id, approvalData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to approve leave'
      );
    }
  }
);

export const denyLeave = createAsyncThunk(
  'leaves/denyLeave',
  async ({ id, denialData }, { rejectWithValue }) => {
    try {
      const response = await leavesService.denyLeave(id, denialData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to deny leave'
      );
    }
  }
);

// Initial state
const initialState = {
  leaveRequests: [],
  currentLeave: null,
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  approveLoading: false,
  denyLoading: false,
};

// Leaves slice
const leavesSlice = createSlice({
  name: 'leaves',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentLeave: (state, action) => {
      state.currentLeave = action.payload;
    },
    clearCurrentLeave: (state) => {
      state.currentLeave = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch leave requests
    builder
      .addCase(fetchLeaveRequests.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLeaveRequests.fulfilled, (state, action) => {
        state.loading = false;
        state.leaveRequests = action.payload.leaves || action.payload;
        state.error = null;
      })
      .addCase(fetchLeaveRequests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Create leave request
    builder
      .addCase(createLeaveRequest.pending, (state) => {
        state.createLoading = true;
        state.error = null;
      })
      .addCase(createLeaveRequest.fulfilled, (state, action) => {
        state.createLoading = false;
        state.leaveRequests.push(action.payload.leave || action.payload);
        state.error = null;
      })
      .addCase(createLeaveRequest.rejected, (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      })
      
    // Fetch leave by ID
    builder
      .addCase(fetchLeaveById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLeaveById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentLeave = action.payload.leave || action.payload;
        state.error = null;
      })
      .addCase(fetchLeaveById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Update leave request
    builder
      .addCase(updateLeaveRequest.pending, (state) => {
        state.updateLoading = true;
        state.error = null;
      })
      .addCase(updateLeaveRequest.fulfilled, (state, action) => {
        state.updateLoading = false;
        const updatedLeave = action.payload.leave || action.payload;
        const index = state.leaveRequests.findIndex(leave => leave.id === updatedLeave.id);
        if (index !== -1) {
          state.leaveRequests[index] = updatedLeave;
        }
        if (state.currentLeave && state.currentLeave.id === updatedLeave.id) {
          state.currentLeave = updatedLeave;
        }
        state.error = null;
      })
      .addCase(updateLeaveRequest.rejected, (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      })
      
    // Approve leave
    builder
      .addCase(approveLeave.pending, (state) => {
        state.approveLoading = true;
        state.error = null;
      })
      .addCase(approveLeave.fulfilled, (state, action) => {
        state.approveLoading = false;
        const approvedLeave = action.payload.leave || action.payload;
        const index = state.leaveRequests.findIndex(leave => leave.id === approvedLeave.id);
        if (index !== -1) {
          state.leaveRequests[index] = approvedLeave;
        }
        if (state.currentLeave && state.currentLeave.id === approvedLeave.id) {
          state.currentLeave = approvedLeave;
        }
        state.error = null;
      })
      .addCase(approveLeave.rejected, (state, action) => {
        state.approveLoading = false;
        state.error = action.payload;
      })
      
    // Deny leave
    builder
      .addCase(denyLeave.pending, (state) => {
        state.denyLoading = true;
        state.error = null;
      })
      .addCase(denyLeave.fulfilled, (state, action) => {
        state.denyLoading = false;
        const deniedLeave = action.payload.leave || action.payload;
        const index = state.leaveRequests.findIndex(leave => leave.id === deniedLeave.id);
        if (index !== -1) {
          state.leaveRequests[index] = deniedLeave;
        }
        if (state.currentLeave && state.currentLeave.id === deniedLeave.id) {
          state.currentLeave = deniedLeave;
        }
        state.error = null;
      })
      .addCase(denyLeave.rejected, (state, action) => {
        state.denyLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, setCurrentLeave, clearCurrentLeave } = leavesSlice.actions;
export default leavesSlice.reducer;
