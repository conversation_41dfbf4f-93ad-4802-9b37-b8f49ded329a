import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { projectsService } from '../../api';

// Async thunks for projects operations
export const fetchProjects = createAsyncThunk(
  'projects/fetchProjects',
  async (_, { rejectWithValue }) => {
    try {
      const response = await projectsService.getProjects();
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch projects'
      );
    }
  }
);

export const createProject = createAsyncThunk(
  'projects/createProject',
  async (projectData, { rejectWithValue }) => {
    try {
      const response = await projectsService.createProject(projectData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to create project'
      );
    }
  }
);

export const fetchProjectById = createAsyncThunk(
  'projects/fetchProjectById',
  async (projectId, { rejectWithValue }) => {
    try {
      const response = await projectsService.getProjectById(projectId);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to fetch project'
      );
    }
  }
);

export const updateProject = createAsyncThunk(
  'projects/updateProject',
  async ({ id, projectData }, { rejectWithValue }) => {
    try {
      const response = await projectsService.updateProject(id, projectData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to update project'
      );
    }
  }
);

export const deleteProject = createAsyncThunk(
  'projects/deleteProject',
  async (projectId, { rejectWithValue }) => {
    try {
      await projectsService.deleteProject(projectId);
      return projectId;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to delete project'
      );
    }
  }
);

export const addProjectMember = createAsyncThunk(
  'projects/addProjectMember',
  async ({ projectId, memberData }, { rejectWithValue }) => {
    try {
      const response = await projectsService.addProjectMember(projectId, memberData);
      return response;
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to add project member'
      );
    }
  }
);

export const removeProjectMember = createAsyncThunk(
  'projects/removeProjectMember',
  async ({ projectId, userId }, { rejectWithValue }) => {
    try {
      await projectsService.removeProjectMember(projectId, userId);
      return { projectId, userId };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || 'Failed to remove project member'
      );
    }
  }
);

// Initial state
const initialState = {
  projects: [],
  currentProject: null,
  loading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false,
  memberLoading: false,
};

// Projects slice
const projectsSlice = createSlice({
  name: 'projects',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentProject: (state, action) => {
      state.currentProject = action.payload;
    },
    clearCurrentProject: (state) => {
      state.currentProject = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch projects
    builder
      .addCase(fetchProjects.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProjects.fulfilled, (state, action) => {
        state.loading = false;
        state.projects = action.payload.projects || action.payload;
        state.error = null;
      })
      .addCase(fetchProjects.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Create project
    builder
      .addCase(createProject.pending, (state) => {
        state.createLoading = true;
        state.error = null;
      })
      .addCase(createProject.fulfilled, (state, action) => {
        state.createLoading = false;
        state.projects.push(action.payload.project || action.payload);
        state.error = null;
      })
      .addCase(createProject.rejected, (state, action) => {
        state.createLoading = false;
        state.error = action.payload;
      })
      
    // Fetch project by ID
    builder
      .addCase(fetchProjectById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProjectById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProject = action.payload.project || action.payload;
        state.error = null;
      })
      .addCase(fetchProjectById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      
    // Update project
    builder
      .addCase(updateProject.pending, (state) => {
        state.updateLoading = true;
        state.error = null;
      })
      .addCase(updateProject.fulfilled, (state, action) => {
        state.updateLoading = false;
        const updatedProject = action.payload.project || action.payload;
        const index = state.projects.findIndex(project => project.id === updatedProject.id);
        if (index !== -1) {
          state.projects[index] = updatedProject;
        }
        if (state.currentProject && state.currentProject.id === updatedProject.id) {
          state.currentProject = updatedProject;
        }
        state.error = null;
      })
      .addCase(updateProject.rejected, (state, action) => {
        state.updateLoading = false;
        state.error = action.payload;
      })
      
    // Delete project
    builder
      .addCase(deleteProject.pending, (state) => {
        state.deleteLoading = true;
        state.error = null;
      })
      .addCase(deleteProject.fulfilled, (state, action) => {
        state.deleteLoading = false;
        state.projects = state.projects.filter(project => project.id !== action.payload);
        if (state.currentProject && state.currentProject.id === action.payload) {
          state.currentProject = null;
        }
        state.error = null;
      })
      .addCase(deleteProject.rejected, (state, action) => {
        state.deleteLoading = false;
        state.error = action.payload;
      })
      
    // Add project member
    builder
      .addCase(addProjectMember.pending, (state) => {
        state.memberLoading = true;
        state.error = null;
      })
      .addCase(addProjectMember.fulfilled, (state, action) => {
        state.memberLoading = false;
        state.error = null;
      })
      .addCase(addProjectMember.rejected, (state, action) => {
        state.memberLoading = false;
        state.error = action.payload;
      })
      
    // Remove project member
    builder
      .addCase(removeProjectMember.pending, (state) => {
        state.memberLoading = true;
        state.error = null;
      })
      .addCase(removeProjectMember.fulfilled, (state, action) => {
        state.memberLoading = false;
        state.error = null;
      })
      .addCase(removeProjectMember.rejected, (state, action) => {
        state.memberLoading = false;
        state.error = action.payload;
      });
  },
});

export const { clearError, setCurrentProject, clearCurrentProject } = projectsSlice.actions;
export default projectsSlice.reducer;
