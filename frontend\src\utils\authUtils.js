/**
 * Utility functions for authentication and user management
 */

/**
 * Check if a user belongs to a company
 * @param {Object} user - The user object
 * @returns {boolean} - True if user has a company, false otherwise
 */
export const userHasCompany = (user) => {
  return user && user.companyId && user.companyId !== null;
};

/**
 * Check if a user needs to complete onboarding (no company)
 * @param {Object} user - The user object
 * @returns {boolean} - True if user needs onboarding, false otherwise
 */
export const userNeedsOnboarding = (user) => {
  return user && (!user.companyId || user.companyId === null);
};

/**
 * Get the appropriate redirect path for a user after login
 * @param {Object} user - The user object
 * @returns {string} - The path to redirect to
 */
export const getRedirectPath = (user) => {
  if (!user) {
    return '/login';
  }
  
  if (userHasCompany(user)) {
    return '/dashboard';
  }
  
  return '/onboarding/company';
};

/**
 * Check if the current route is an onboarding route
 * @param {string} pathname - The current pathname
 * @returns {boolean} - True if it's an onboarding route
 */
export const isOnboardingRoute = (pathname) => {
  return pathname && pathname.startsWith('/onboarding');
};
